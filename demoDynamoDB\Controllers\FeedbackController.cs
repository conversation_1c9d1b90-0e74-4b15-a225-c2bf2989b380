using Microsoft.AspNetCore.Mvc;
using demoDynamoDB.Models;
using demoDynamoDB.Services;

namespace demoDynamoDB.Controllers
{
    public class FeedbackController : Controller
    {
        private readonly IFeedbackService _feedbackService;
        private readonly ILogger<FeedbackController> _logger;

        public FeedbackController(IFeedbackService feedbackService, ILogger<FeedbackController> logger)
        {
            _feedbackService = feedbackService;
            _logger = logger;
        }

        // GET: Feedback
        public async Task<IActionResult> Index(string searchTerm = "", string category = "", int? rating = null, string status = "")
        {
            try
            {
                var model = await _feedbackService.GetFeedbackListAsync(searchTerm, category, rating, status);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feedback list");
                TempData["ErrorMessage"] = "An error occurred while retrieving feedback data.";
                return View(new FeedbackListViewModel());
            }
        }

        // GET: Feedback/Details/5
        public async Task<IActionResult> Details(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var feedback = await _feedbackService.GetFeedbackByIdAsync(id);
                if (feedback == null)
                {
                    return NotFound();
                }

                return View(feedback);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feedback details for ID: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while retrieving feedback details.";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Feedback/Create
        public IActionResult Create()
        {
            var model = new CreateFeedbackViewModel();
            return View(model);
        }

        // POST: Feedback/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateFeedbackViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await _feedbackService.CreateFeedbackAsync(model);
                    TempData["SuccessMessage"] = "Feedback submitted successfully!";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating feedback");
                    ModelState.AddModelError("", "An error occurred while submitting your feedback. Please try again.");
                }
            }

            return View(model);
        }

        // GET: Feedback/Edit/5
        public async Task<IActionResult> Edit(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var feedback = await _feedbackService.GetFeedbackByIdAsync(id);
                if (feedback == null)
                {
                    return NotFound();
                }

                var model = new EditFeedbackViewModel
                {
                    Id = feedback.Id,
                    CustomerName = feedback.CustomerName,
                    Email = feedback.Email,
                    FeedbackText = feedback.FeedbackText,
                    Rating = feedback.Rating,
                    Category = feedback.Category,
                    Status = feedback.Status,
                    Timestamp = feedback.Timestamp
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feedback for editing. ID: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while retrieving feedback data.";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Feedback/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string id, EditFeedbackViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _feedbackService.UpdateFeedbackAsync(model);
                    TempData["SuccessMessage"] = "Feedback updated successfully!";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating feedback. ID: {Id}", id);
                    ModelState.AddModelError("", "An error occurred while updating the feedback. Please try again.");
                }
            }

            return View(model);
        }

        // GET: Feedback/Delete/5
        public async Task<IActionResult> Delete(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var feedback = await _feedbackService.GetFeedbackByIdAsync(id);
                if (feedback == null)
                {
                    return NotFound();
                }

                return View(feedback);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feedback for deletion. ID: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while retrieving feedback data.";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Feedback/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(string id)
        {
            try
            {
                var result = await _feedbackService.DeleteFeedbackAsync(id);
                if (result)
                {
                    TempData["SuccessMessage"] = "Feedback deleted successfully!";
                }
                else
                {
                    TempData["ErrorMessage"] = "Failed to delete feedback.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting feedback. ID: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while deleting the feedback.";
            }

            return RedirectToAction(nameof(Index));
        }

        // API endpoint for AJAX calls
        [HttpGet]
        public async Task<IActionResult> GetFeedbackData(string searchTerm = "", string category = "", int? rating = null, string status = "")
        {
            try
            {
                var model = await _feedbackService.GetFeedbackListAsync(searchTerm, category, rating, status);
                return Json(new { success = true, data = model.Feedbacks, totalCount = model.TotalCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feedback data via API");
                return Json(new { success = false, message = "An error occurred while retrieving feedback data." });
            }
        }
    }
}
