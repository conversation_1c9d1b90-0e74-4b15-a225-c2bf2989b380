using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public class FeedbackService : IFeedbackService
    {
        private readonly IDynamoDBService _dynamoDBService;

        public FeedbackService(IDynamoDBService dynamoDBService)
        {
            _dynamoDBService = dynamoDBService;
        }

        public async Task<FeedbackListViewModel> GetFeedbackListAsync(string searchTerm = "", string category = "", int? rating = null, string status = "")
        {
            var feedbacks = await _dynamoDBService.SearchFeedbacksAsync(searchTerm, category, rating, status);
            var categories = await GetCategoriesAsync();
            var statuses = await GetStatusesAsync();

            return new FeedbackListViewModel
            {
                Feedbacks = feedbacks,
                SearchTerm = searchTerm,
                CategoryFilter = category,
                RatingFilter = rating,
                StatusFilter = status,
                TotalCount = feedbacks.Count,
                Categories = categories,
                Statuses = statuses
            };
        }

        public async Task<CustomerFeedback?> GetFeedbackByIdAsync(string id)
        {
            return await _dynamoDBService.GetFeedbackByIdAsync(id);
        }

        public async Task<CustomerFeedback> CreateFeedbackAsync(CreateFeedbackViewModel model)
        {
            var feedback = new CustomerFeedback
            {
                CustomerName = model.CustomerName,
                Email = model.Email,
                FeedbackText = model.FeedbackText,
                Rating = model.Rating,
                Category = model.Category,
                Status = "New",
                Timestamp = DateTime.UtcNow
            };

            return await _dynamoDBService.CreateFeedbackAsync(feedback);
        }

        public async Task<CustomerFeedback> UpdateFeedbackAsync(EditFeedbackViewModel model)
        {
            var feedback = new CustomerFeedback
            {
                Id = model.Id,
                CustomerName = model.CustomerName,
                Email = model.Email,
                FeedbackText = model.FeedbackText,
                Rating = model.Rating,
                Category = model.Category,
                Status = model.Status,
                Timestamp = model.Timestamp
            };

            return await _dynamoDBService.UpdateFeedbackAsync(feedback);
        }

        public async Task<bool> DeleteFeedbackAsync(string id)
        {
            return await _dynamoDBService.DeleteFeedbackAsync(id);
        }

        public async Task<DashboardViewModel> GetDashboardAsync()
        {
            return await _dynamoDBService.GetDashboardStatsAsync();
        }

        public async Task<List<string>> GetCategoriesAsync()
        {
            var feedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var categories = feedbacks.Select(f => f.Category).Distinct().OrderBy(c => c).ToList();
            
            // Add default categories if they don't exist
            var defaultCategories = new[] { "General", "Product Quality", "Customer Service", "Delivery", "Website", "Pricing", "Other" };
            foreach (var category in defaultCategories)
            {
                if (!categories.Contains(category))
                {
                    categories.Add(category);
                }
            }
            
            return categories.OrderBy(c => c).ToList();
        }

        public async Task<List<string>> GetStatusesAsync()
        {
            var feedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var statuses = feedbacks.Select(f => f.Status).Distinct().OrderBy(s => s).ToList();
            
            // Add default statuses if they don't exist
            var defaultStatuses = new[] { "New", "In Progress", "Resolved", "Closed" };
            foreach (var status in defaultStatuses)
            {
                if (!statuses.Contains(status))
                {
                    statuses.Add(status);
                }
            }
            
            return statuses.OrderBy(s => s).ToList();
        }
    }
}
