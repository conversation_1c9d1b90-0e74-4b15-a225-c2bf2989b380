# Database Schema Documentation

## Overview
This document describes the DynamoDB table structure used by the Customer Feedback Management System.

## Table: CustomerFeedback

### Table Configuration
- **Table Name**: `CustomerFeedback`
- **Billing Mode**: Pay-per-request (On-demand)
- **Primary Key**: Simple primary key using hash key only
- **Region**: us-east-1 (for local DynamoDB)

### Key Schema
```
Hash Key (Partition Key): Id (String)
```

### Attributes

| Attribute Name | Type | Required | Description | Validation |
|---------------|------|----------|-------------|------------|
| `Id` | String | Yes | Unique identifier for feedback entry | GUID format |
| `CustomerName` | String | Yes | Customer's full name | Max 100 characters |
| `Email` | String | Yes | Customer's email address | Valid email format |
| `FeedbackText` | String | Yes | Detailed feedback content | Max 1000 characters |
| `Rating` | Number | Yes | Customer rating | Integer 1-5 |
| `Category` | String | No | Feedback category | Max 50 characters |
| `Status` | String | No | Current processing status | Predefined values |
| `Timestamp` | String | Yes | When feedback was submitted | ISO 8601 format |

### Attribute Details

#### Id (Hash Key)
- **Type**: String
- **Format**: GUID (e.g., "123e4567-e89b-12d3-a456-************")
- **Purpose**: Unique identifier for each feedback entry
- **Generation**: Auto-generated using `Guid.NewGuid().ToString()`

#### CustomerName
- **Type**: String
- **Constraints**: 
  - Required field
  - Maximum length: 100 characters
  - Minimum length: 1 character (after trimming)
- **Example**: "John Smith"

#### Email
- **Type**: String
- **Constraints**:
  - Required field
  - Must be valid email format
  - Case-insensitive storage
- **Example**: "<EMAIL>"

#### FeedbackText
- **Type**: String
- **Constraints**:
  - Required field
  - Maximum length: 1000 characters
  - Minimum length: 1 character (after trimming)
- **Example**: "Great product quality and fast delivery. Very satisfied with my purchase!"

#### Rating
- **Type**: Number
- **Constraints**:
  - Required field
  - Integer value between 1 and 5 (inclusive)
  - 1 = Very Poor, 2 = Poor, 3 = Average, 4 = Good, 5 = Excellent
- **Example**: 5

#### Category
- **Type**: String
- **Constraints**:
  - Optional field
  - Maximum length: 50 characters
  - Default value: "General"
- **Predefined Categories**:
  - General
  - Product Quality
  - Customer Service
  - Delivery
  - Website
  - Pricing
  - Other
- **Example**: "Product Quality"

#### Status
- **Type**: String
- **Constraints**:
  - Optional field
  - Default value: "New"
- **Valid Values**:
  - "New" - Newly submitted feedback
  - "In Progress" - Currently being reviewed/processed
  - "Resolved" - Issue has been addressed
  - "Closed" - Feedback processing completed
- **Example**: "New"

#### Timestamp
- **Type**: String
- **Format**: ISO 8601 UTC format
- **Constraints**:
  - Required field
  - Auto-generated on creation
  - Immutable after creation
- **Example**: "2025-01-30T10:30:00.000Z"

## Sample Data Structure

```json
{
  "Id": "123e4567-e89b-12d3-a456-************",
  "CustomerName": "John Smith",
  "Email": "<EMAIL>",
  "FeedbackText": "Great product quality and fast delivery. Very satisfied with my purchase!",
  "Rating": 5,
  "Category": "Product Quality",
  "Status": "Resolved",
  "Timestamp": "2025-01-25T14:30:00.000Z"
}
```

## Access Patterns

### Primary Access Pattern
- **Query by ID**: Direct lookup using the hash key
- **Use Case**: Retrieve specific feedback entry
- **Performance**: O(1) - Single item lookup

### Secondary Access Patterns
Since DynamoDB Local is used for demonstration, the following patterns use scan operations with filters:

1. **List All Feedback**
   - Operation: Scan
   - Filter: None
   - Sort: By Timestamp (descending)

2. **Filter by Status**
   - Operation: Scan
   - Filter: Status = {value}
   - Use Case: View feedback by processing status

3. **Filter by Category**
   - Operation: Scan
   - Filter: Category = {value}
   - Use Case: View feedback by category

4. **Filter by Rating**
   - Operation: Scan
   - Filter: Rating = {value}
   - Use Case: View feedback by rating level

5. **Text Search**
   - Operation: Scan
   - Filter: Contains search term in CustomerName, Email, or FeedbackText
   - Use Case: Search across feedback content

## Performance Considerations

### For Production Use
In a production environment, consider adding Global Secondary Indexes (GSI) for better query performance:

1. **Status-Timestamp-Index**
   - Hash Key: Status
   - Sort Key: Timestamp
   - Purpose: Efficient queries by status with time ordering

2. **Category-Rating-Index**
   - Hash Key: Category
   - Sort Key: Rating
   - Purpose: Efficient queries by category and rating

3. **Email-Index**
   - Hash Key: Email
   - Purpose: Customer lookup by email

### Current Implementation
The current implementation uses scan operations which are suitable for:
- Small datasets (< 1000 items)
- Development and testing
- Demonstration purposes

## Data Validation

### Application Level
- Model validation attributes in `CustomerFeedback.cs`
- Client-side validation in forms
- Server-side validation in controllers

### Database Level
- DynamoDB attribute types enforce basic type validation
- Application logic enforces business rules

## Backup and Recovery

### Local Development
- Data is stored in DynamoDB Local files
- No automatic backup (development only)
- Data persists between application restarts

### Production Recommendations
- Enable DynamoDB Point-in-Time Recovery
- Set up automated backups
- Implement cross-region replication if needed

## Migration Considerations

### Schema Changes
- DynamoDB is schema-less for non-key attributes
- New attributes can be added without migration
- Key schema changes require table recreation

### Data Migration
- Export/import tools available for DynamoDB
- AWS Data Pipeline for complex migrations
- Custom scripts for data transformation

## Monitoring and Metrics

### Key Metrics to Monitor
- Read/Write capacity utilization
- Throttled requests
- Item count and table size
- Query/Scan performance

### CloudWatch Integration
- Automatic metrics collection
- Custom alarms for capacity and errors
- Performance insights dashboard
