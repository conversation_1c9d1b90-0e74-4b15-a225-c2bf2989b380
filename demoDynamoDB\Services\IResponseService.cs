using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public interface IResponseService
    {
        Task<CustomerResponse> CreateResponseAsync(ResponseViewModel model);
        Task<List<CustomerResponse>> GetResponsesByFeedbackIdAsync(string feedbackId);
        Task<CustomerResponse?> GetResponseByIdAsync(string responseId);
        Task<CustomerResponse> UpdateResponseAsync(CustomerResponse response);
        Task<bool> DeleteResponseAsync(string responseId);
        Task<List<CustomerResponse>> GetAllResponsesAsync();
        Task<ManagementDashboardViewModel> GetManagementDashboardAsync();
        Task<BulkEmailResult> ProcessBulkActionAsync(BulkActionViewModel model);
        Task<bool> EscalateFeedbackAsync(string feedbackId, string reason);
        Task<bool> AssignFeedbackAsync(string feedbackId, string adminName);
        Task<List<CustomerFeedback>> GetFeedbacksRequiringAttentionAsync();
    }
}
