@model demoDynamoDB.Models.CustomerFeedback
@{
    ViewData["Title"] = "Delete Feedback";
}

<div class="container feedback-details-container content-with-footer-spacing">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger feedback-card-container">
                <div class="card-header bg-danger text-white">
                    <h4><i class="fas fa-exclamation-triangle"></i> Delete Feedback</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-warning"></i>
                        <strong>Warning!</strong> Are you sure you want to delete this feedback? This action cannot be undone.
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Customer Information</h6>
                            <div class="mb-2">
                                <strong>Name:</strong> @Model.CustomerName
                            </div>
                            <div class="mb-2">
                                <strong>Email:</strong> @Model.Email
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Feedback Information</h6>
                            <div class="mb-2">
                                <strong>Category:</strong> 
                                <span class="badge bg-light text-dark">@Model.Category</span>
                            </div>
                            <div class="mb-2">
                                <strong>Rating:</strong> 
                                <span class="fs-5">@Model.RatingStars</span>
                                <span class="text-muted">(@Model.Rating/5)</span>
                            </div>
                            <div class="mb-2">
                                <strong>Status:</strong> 
                                <span class="badge bg-@GetStatusBadgeClass(Model.Status)">@Model.Status</span>
                            </div>
                            <div class="mb-2">
                                <strong>Submitted:</strong> @Model.FormattedTimestamp
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-muted">Feedback Content</h6>
                        <div class="border rounded p-3 bg-light">
                            <p class="mb-0">@Model.FeedbackText</p>
                        </div>
                    </div>

                    <form asp-action="Delete" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <div>
                                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete this feedback?')">
                                    <i class="fas fa-trash"></i> Delete Feedback
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Impact Warning -->
            <div class="card mt-4 border-warning feedback-card-container">
                <div class="card-header bg-warning text-dark">
                    <h6><i class="fas fa-info-circle"></i> Deletion Impact</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>This feedback will be permanently removed from the database</li>
                        <li>All associated data and history will be lost</li>
                        <li>This action cannot be reversed</li>
                        <li>Consider changing the status to "Closed" instead if you want to keep the record</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "New" => "primary",
            "In Progress" => "warning",
            "Resolved" => "success",
            "Closed" => "secondary",
            _ => "light"
        };
    }
}
