using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.Runtime;
using demoDynamoDB.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Configure DynamoDB
var dynamoDbConfig = new AmazonDynamoDBConfig();
var localServiceUrl = builder.Configuration["DynamoDB:LocalServiceUrl"];
if (!string.IsNullOrEmpty(localServiceUrl))
{
    dynamoDbConfig.ServiceURL = localServiceUrl;
}

// For DynamoDB Local, we need to provide dummy credentials
builder.Services.AddSingleton<IAmazonDynamoDB>(provider =>
{
    var credentials = new Amazon.Runtime.BasicAWSCredentials("dummy", "dummy");
    return new AmazonDynamoDBClient(credentials, dynamoDbConfig);
});
builder.Services.AddSingleton<DynamoDBContext>(provider =>
{
    var client = provider.GetRequiredService<IAmazonDynamoDB>();
    return new DynamoDBContext(client);
});

// Register application services
builder.Services.AddScoped<IDynamoDBService, DynamoDBService>();
builder.Services.AddScoped<IFeedbackService, FeedbackService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IResponseService, ResponseService>();

var app = builder.Build();

// Initialize DynamoDB table
using (var scope = app.Services.CreateScope())
{
    var dynamoDBService = scope.ServiceProvider.GetRequiredService<IDynamoDBService>();
    await dynamoDBService.InitializeTableAsync();
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseRouting();

app.UseAuthorization();

app.MapStaticAssets();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();


app.Run();
