namespace demoDynamoDB.Models
{
    public class DashboardViewModel
    {
        public int TotalFeedbacks { get; set; }
        public double AverageRating { get; set; }
        public int NewFeedbacks { get; set; }
        public int InProgressFeedbacks { get; set; }
        public int ResolvedFeedbacks { get; set; }
        public int ClosedFeedbacks { get; set; }
        
        public List<CategoryStats> CategoryStats { get; set; } = new();
        public List<RatingStats> RatingStats { get; set; } = new();
        public List<CustomerFeedback> RecentFeedbacks { get; set; } = new();
        
        public string FormattedAverageRating => AverageRating.ToString("F1");
        public string AverageRatingStars => new string('★', (int)Math.Round(AverageRating)) + 
                                           new string('☆', 5 - (int)Math.Round(AverageRating));
    }

    public class CategoryStats
    {
        public string Category { get; set; } = string.Empty;
        public int Count { get; set; }
        public double AverageRating { get; set; }
        public string FormattedAverageRating => AverageRating.ToString("F1");
    }

    public class RatingStats
    {
        public int Rating { get; set; }
        public int Count { get; set; }
        public double Percentage { get; set; }
        public string FormattedPercentage => Percentage.ToString("F1");
    }
}
