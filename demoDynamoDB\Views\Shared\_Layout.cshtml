﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Customer Feedback System</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/demoDynamoDB.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-primary border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-comments"></i> Customer Feedback System
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Feedback" asp-action="Index">
                                <i class="fas fa-list"></i> All Feedback
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Feedback" asp-action="Create">
                                <i class="fas fa-plus"></i> Add Feedback
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-filter"></i> Quick Filters
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" asp-controller="Feedback" asp-action="Index" asp-route-status="New">
                                    <i class="fas fa-bell text-primary"></i> New Feedback
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Feedback" asp-action="Index" asp-route-status="In Progress">
                                    <i class="fas fa-cog text-warning"></i> In Progress
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Feedback" asp-action="Index" asp-route-rating="1">
                                    <i class="fas fa-exclamation-triangle text-danger"></i> Low Ratings
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Feedback" asp-action="Index" asp-route-rating="5">
                                    <i class="fas fa-star text-success"></i> High Ratings
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="http://localhost:8001" target="_blank">
                                <i class="fas fa-database"></i> DynamoDB Admin
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="main-content-wrapper">
        <div class="container-fluid">
            <main role="main" class="pb-3">
                @RenderBody()
            </main>
        </div>
    </div>

    <!-- Footer temporarily commented out to avoid positioning issues -->
    <!--
    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - demoDynamoDB - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
