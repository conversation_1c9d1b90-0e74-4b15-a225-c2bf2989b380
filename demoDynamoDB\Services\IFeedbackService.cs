using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public interface IFeedbackService
    {
        Task<FeedbackListViewModel> GetFeedbackListAsync(string searchTerm = "", string category = "", int? rating = null, string status = "");
        Task<FeedbackListViewModel> GetAdvancedFeedbackListAsync(
            string searchTerm = "",
            string category = "",
            int? rating = null,
            string status = "",
            string priority = "",
            string responseStatus = "",
            string assignedTo = "",
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            bool escalatedOnly = false,
            string sortBy = "Timestamp",
            string sortDirection = "desc");
        Task<CustomerFeedback?> GetFeedbackByIdAsync(string id);
        Task<CustomerFeedback> CreateFeedbackAsync(CreateFeedbackViewModel model);
        Task<CustomerFeedback> UpdateFeedbackAsync(EditFeedbackViewModel model);
        Task<bool> DeleteFeedbackAsync(string id);
        Task<DashboardViewModel> GetDashboardAsync();
        Task<List<string>> GetCategoriesAsync();
        Task<List<string>> GetStatusesAsync();
        Task<List<string>> GetPrioritiesAsync();
        Task<List<string>> GetResponseStatusesAsync();
        Task<List<string>> GetAdministratorsAsync();
    }
}
