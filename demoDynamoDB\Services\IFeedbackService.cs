using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public interface IFeedbackService
    {
        Task<FeedbackListViewModel> GetFeedbackListAsync(string searchTerm = "", string category = "", int? rating = null, string status = "");
        Task<CustomerFeedback?> GetFeedbackByIdAsync(string id);
        Task<CustomerFeedback> CreateFeedbackAsync(CreateFeedbackViewModel model);
        Task<CustomerFeedback> UpdateFeedbackAsync(EditFeedbackViewModel model);
        Task<bool> DeleteFeedbackAsync(string id);
        Task<DashboardViewModel> GetDashboardAsync();
        Task<List<string>> GetCategoriesAsync();
        Task<List<string>> GetStatusesAsync();
    }
}
