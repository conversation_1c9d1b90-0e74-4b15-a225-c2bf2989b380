using System.ComponentModel.DataAnnotations;

namespace demoDynamoDB.Models
{
    public class EmailConfiguration
    {
        public string SmtpServer { get; set; } = string.Empty;
        public int SmtpPort { get; set; } = 587;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool EnableSsl { get; set; } = true;
        public string FromEmail { get; set; } = string.Empty;
        public string FromName { get; set; } = "Customer Feedback Team";
    }

    public class EmailTemplate
    {
        public string Type { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
    }

    public class SendEmailRequest
    {
        [Required(ErrorMessage = "Feedback ID is required")]
        public string FeedbackId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Response ID is required")]
        public string ResponseId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email template type is required")]
        public string TemplateType { get; set; } = "Standard";

        public string CustomSubject { get; set; } = string.Empty;
        public string CustomMessage { get; set; } = string.Empty;
    }

    public class EmailSendResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime SentDate { get; set; }
        public string EmailAddress { get; set; } = string.Empty;
    }

    public class BulkEmailRequest
    {
        [Required]
        public List<string> FeedbackIds { get; set; } = new();

        [Required(ErrorMessage = "Template type is required")]
        public string TemplateType { get; set; } = "Standard";

        public string CustomSubject { get; set; } = string.Empty;
        public string CustomMessage { get; set; } = string.Empty;
    }

    public class BulkEmailResult
    {
        public int TotalEmails { get; set; }
        public int SuccessfulEmails { get; set; }
        public int FailedEmails { get; set; }
        public List<EmailSendResult> Results { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }
}
