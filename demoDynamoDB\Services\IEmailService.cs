using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public interface IEmailService
    {
        Task<EmailSendResult> SendResponseEmailAsync(CustomerFeedback feedback, CustomerResponse response, string templateType = "Standard");
        Task<EmailSendResult> SendCustomEmailAsync(string toEmail, string subject, string body, string customerName = "");
        Task<BulkEmailResult> SendBulkEmailsAsync(List<CustomerFeedback> feedbacks, string templateType, string customSubject = "", string customMessage = "");
        Task<bool> TestEmailConfigurationAsync();
        string GenerateEmailBody(CustomerFeedback feedback, CustomerResponse response, string templateType);
        string GenerateEmailSubject(CustomerFeedback feedback, string templateType);
        Dictionary<string, EmailTemplate> GetEmailTemplates();
    }
}
