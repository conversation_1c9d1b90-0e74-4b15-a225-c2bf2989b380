using Amazon.DynamoDBv2.DataModel;
using System.ComponentModel.DataAnnotations;

namespace demoDynamoDB.Models
{
    [DynamoDBTable("CustomerResponse")]
    public class CustomerResponse
    {
        [DynamoDBHashKey]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [DynamoDBProperty]
        [Required(ErrorMessage = "Feedback ID is required")]
        public string FeedbackId { get; set; } = string.Empty;

        [DynamoDBProperty]
        [Required(ErrorMessage = "Response text is required")]
        [StringLength(2000, ErrorMessage = "Response cannot exceed 2000 characters")]
        public string ResponseText { get; set; } = string.Empty;

        [DynamoDBProperty]
        [Required(ErrorMessage = "Administrator name is required")]
        [StringLength(100, ErrorMessage = "Administrator name cannot exceed 100 characters")]
        public string AdminName { get; set; } = string.Empty;

        [DynamoDBProperty]
        public DateTime ResponseDate { get; set; } = DateTime.UtcNow;

        [DynamoDBProperty]
        [StringLength(50, ErrorMessage = "Response type cannot exceed 50 characters")]
        public string ResponseType { get; set; } = "Standard";

        [DynamoDBProperty]
        public bool IsEmailSent { get; set; } = false;

        [DynamoDBProperty]
        public DateTime? EmailSentDate { get; set; }

        [DynamoDBProperty]
        public string EmailStatus { get; set; } = "Pending";

        [DynamoDBProperty]
        public bool IsInternal { get; set; } = false;

        // Helper properties for display
        [DynamoDBIgnore]
        public string FormattedResponseDate => ResponseDate.ToString("yyyy-MM-dd HH:mm:ss");

        [DynamoDBIgnore]
        public string FormattedEmailSentDate => EmailSentDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Not sent";

        [DynamoDBIgnore]
        public string ResponseTypeBadgeClass => ResponseType switch
        {
            "Acknowledgment" => "info",
            "Resolution" => "success",
            "Follow-up" => "warning",
            "Escalation" => "danger",
            "Standard" => "primary",
            _ => "secondary"
        };

        [DynamoDBIgnore]
        public string EmailStatusBadgeClass => EmailStatus switch
        {
            "Sent" => "success",
            "Failed" => "danger",
            "Pending" => "warning",
            _ => "secondary"
        };
    }
}
