using demoDynamoDB.Models;
using System.Net;
using System.Net.Mail;
using System.Text;

namespace demoDynamoDB.Services
{
    public class EmailService : IEmailService
    {
        private readonly EmailConfiguration _emailConfig;
        private readonly ILogger<EmailService> _logger;
        private readonly Dictionary<string, EmailTemplate> _emailTemplates;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
        {
            _emailConfig = configuration.GetSection("Email").Get<EmailConfiguration>() ?? new EmailConfiguration();
            _logger = logger;
            _emailTemplates = InitializeEmailTemplates();
        }

        public async Task<EmailSendResult> SendResponseEmailAsync(CustomerFeedback feedback, CustomerResponse response, string templateType = "Standard")
        {
            try
            {
                var subject = GenerateEmailSubject(feedback, templateType);
                var body = GenerateEmailBody(feedback, response, templateType);

                return await SendEmailAsync(feedback.Email, subject, body, feedback.CustomerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send response email for feedback {FeedbackId}", feedback.Id);
                return new EmailSendResult
                {
                    Success = false,
                    Message = $"Failed to send email: {ex.Message}",
                    EmailAddress = feedback.Email
                };
            }
        }

        public async Task<EmailSendResult> SendCustomEmailAsync(string toEmail, string subject, string body, string customerName = "")
        {
            return await SendEmailAsync(toEmail, subject, body, customerName);
        }

        public async Task<BulkEmailResult> SendBulkEmailsAsync(List<CustomerFeedback> feedbacks, string templateType, string customSubject = "", string customMessage = "")
        {
            var result = new BulkEmailResult
            {
                TotalEmails = feedbacks.Count
            };

            foreach (var feedback in feedbacks)
            {
                try
                {
                    var subject = !string.IsNullOrEmpty(customSubject) ? customSubject : GenerateEmailSubject(feedback, templateType);
                    var body = !string.IsNullOrEmpty(customMessage) ? customMessage : GenerateBulkEmailBody(feedback, templateType);

                    var emailResult = await SendEmailAsync(feedback.Email, subject, body, feedback.CustomerName);
                    result.Results.Add(emailResult);

                    if (emailResult.Success)
                    {
                        result.SuccessfulEmails++;
                    }
                    else
                    {
                        result.FailedEmails++;
                        result.Errors.Add($"{feedback.Email}: {emailResult.Message}");
                    }
                }
                catch (Exception ex)
                {
                    result.FailedEmails++;
                    result.Errors.Add($"{feedback.Email}: {ex.Message}");
                    _logger.LogError(ex, "Failed to send bulk email to {Email}", feedback.Email);
                }

                // Add small delay between emails to avoid overwhelming SMTP server
                await Task.Delay(100);
            }

            return result;
        }

        public async Task<bool> TestEmailConfigurationAsync()
        {
            try
            {
                using var client = CreateSmtpClient();
                // Just test the connection without sending an email
                await client.SendMailAsync(new MailMessage(_emailConfig.FromEmail, _emailConfig.FromEmail, "Test", "Test"));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Email configuration test failed");
                return false;
            }
        }

        public string GenerateEmailSubject(CustomerFeedback feedback, string templateType)
        {
            if (_emailTemplates.TryGetValue(templateType, out var template))
            {
                return template.Subject
                    .Replace("{CustomerName}", feedback.CustomerName)
                    .Replace("{FeedbackId}", feedback.Id[..8])
                    .Replace("{Category}", feedback.Category);
            }

            return $"Response to your feedback - {feedback.Category}";
        }

        public string GenerateEmailBody(CustomerFeedback feedback, CustomerResponse response, string templateType)
        {
            if (_emailTemplates.TryGetValue(templateType, out var template))
            {
                return template.Body
                    .Replace("{CustomerName}", feedback.CustomerName)
                    .Replace("{OriginalFeedback}", feedback.FeedbackText)
                    .Replace("{Response}", response.ResponseText)
                    .Replace("{AdminName}", response.AdminName)
                    .Replace("{FeedbackId}", feedback.Id[..8])
                    .Replace("{Category}", feedback.Category)
                    .Replace("{Rating}", feedback.Rating.ToString())
                    .Replace("{ResponseDate}", response.ResponseDate.ToString("yyyy-MM-dd HH:mm"));
            }

            return GenerateDefaultEmailBody(feedback, response);
        }

        public Dictionary<string, EmailTemplate> GetEmailTemplates()
        {
            return new Dictionary<string, EmailTemplate>(_emailTemplates);
        }

        private async Task<EmailSendResult> SendEmailAsync(string toEmail, string subject, string body, string customerName)
        {
            try
            {
                using var client = CreateSmtpClient();
                using var message = new MailMessage();

                message.From = new MailAddress(_emailConfig.FromEmail, _emailConfig.FromName);
                message.To.Add(new MailAddress(toEmail, customerName));
                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = true;

                await client.SendMailAsync(message);

                return new EmailSendResult
                {
                    Success = true,
                    Message = "Email sent successfully",
                    SentDate = DateTime.UtcNow,
                    EmailAddress = toEmail
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Email}", toEmail);
                return new EmailSendResult
                {
                    Success = false,
                    Message = ex.Message,
                    EmailAddress = toEmail
                };
            }
        }

        private SmtpClient CreateSmtpClient()
        {
            var client = new SmtpClient(_emailConfig.SmtpServer, _emailConfig.SmtpPort)
            {
                Credentials = new NetworkCredential(_emailConfig.Username, _emailConfig.Password),
                EnableSsl = _emailConfig.EnableSsl
            };
            return client;
        }

        private string GenerateDefaultEmailBody(CustomerFeedback feedback, CustomerResponse response)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"<h2>Response to Your Feedback</h2>");
            sb.AppendLine($"<p>Dear {feedback.CustomerName},</p>");
            sb.AppendLine($"<p>Thank you for your feedback. We have reviewed your message and would like to respond:</p>");
            sb.AppendLine($"<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>");
            sb.AppendLine($"<strong>Your Original Feedback:</strong><br>");
            sb.AppendLine($"{feedback.FeedbackText}");
            sb.AppendLine($"</div>");
            sb.AppendLine($"<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>");
            sb.AppendLine($"<strong>Our Response:</strong><br>");
            sb.AppendLine($"{response.ResponseText}");
            sb.AppendLine($"</div>");
            sb.AppendLine($"<p>If you have any further questions, please don't hesitate to contact us.</p>");
            sb.AppendLine($"<p>Best regards,<br>{response.AdminName}<br>{_emailConfig.FromName}</p>");
            return sb.ToString();
        }

        private string GenerateBulkEmailBody(CustomerFeedback feedback, string templateType)
        {
            if (_emailTemplates.TryGetValue(templateType, out var template))
            {
                return template.Body
                    .Replace("{CustomerName}", feedback.CustomerName)
                    .Replace("{OriginalFeedback}", feedback.FeedbackText)
                    .Replace("{FeedbackId}", feedback.Id[..8])
                    .Replace("{Category}", feedback.Category)
                    .Replace("{Rating}", feedback.Rating.ToString());
            }

            return $"<p>Dear {feedback.CustomerName},</p><p>Thank you for your feedback. We are reviewing your message and will respond soon.</p>";
        }

        private Dictionary<string, EmailTemplate> InitializeEmailTemplates()
        {
            return new Dictionary<string, EmailTemplate>
            {
                ["Standard"] = new EmailTemplate
                {
                    Type = "Standard",
                    Subject = "Response to your feedback - {Category}",
                    Body = @"<h2>Response to Your Feedback</h2>
                            <p>Dear {CustomerName},</p>
                            <p>Thank you for your feedback. We have reviewed your message and would like to respond:</p>
                            <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>
                                <strong>Your Original Feedback:</strong><br>{OriginalFeedback}
                            </div>
                            <div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
                                <strong>Our Response:</strong><br>{Response}
                            </div>
                            <p>Best regards,<br>{AdminName}<br>Customer Feedback Team</p>"
                },
                ["Acknowledgment"] = new EmailTemplate
                {
                    Type = "Acknowledgment",
                    Subject = "We received your feedback - {Category}",
                    Body = @"<h2>Thank You for Your Feedback</h2>
                            <p>Dear {CustomerName},</p>
                            <p>We have received your feedback and want to acknowledge that we take your concerns seriously.</p>
                            <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>
                                <strong>Your Feedback:</strong><br>{OriginalFeedback}
                            </div>
                            <p>We are currently reviewing your message and will provide a detailed response within 24-48 hours.</p>
                            <p>Best regards,<br>Customer Feedback Team</p>"
                },
                ["Resolution"] = new EmailTemplate
                {
                    Type = "Resolution",
                    Subject = "Your issue has been resolved - {Category}",
                    Body = @"<h2>Issue Resolution</h2>
                            <p>Dear {CustomerName},</p>
                            <p>We are pleased to inform you that we have resolved the issue you reported:</p>
                            <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>
                                <strong>Your Original Feedback:</strong><br>{OriginalFeedback}
                            </div>
                            <div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
                                <strong>Resolution Details:</strong><br>{Response}
                            </div>
                            <p>If you experience any further issues, please don't hesitate to contact us.</p>
                            <p>Best regards,<br>{AdminName}<br>Customer Feedback Team</p>"
                }
            };
        }
    }
}
