using Microsoft.AspNetCore.Mvc;
using demoDynamoDB.Models;
using demoDynamoDB.Services;

namespace demoDynamoDB.Controllers
{
    public class ManagementController : Controller
    {
        private readonly IResponseService _responseService;
        private readonly IFeedbackService _feedbackService;
        private readonly IEmailService _emailService;
        private readonly ILogger<ManagementController> _logger;

        public ManagementController(
            IResponseService responseService, 
            IFeedbackService feedbackService,
            IEmailService emailService,
            ILogger<ManagementController> logger)
        {
            _responseService = responseService;
            _feedbackService = feedbackService;
            _emailService = emailService;
            _logger = logger;
        }

        // GET: Management Dashboard
        public async Task<IActionResult> Dashboard()
        {
            try
            {
                var dashboard = await _responseService.GetManagementDashboardAsync();
                return View(dashboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading management dashboard");
                TempData["ErrorMessage"] = "An error occurred while loading the management dashboard.";
                return View(new ManagementDashboardViewModel());
            }
        }

        // GET: Management/Feedbacks - Advanced feedback management
        public async Task<IActionResult> Feedbacks(
            string searchTerm = "", 
            string category = "", 
            int? rating = null, 
            string status = "",
            string priority = "",
            string responseStatus = "",
            string assignedTo = "",
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            bool escalatedOnly = false,
            string sortBy = "Timestamp",
            string sortDirection = "desc")
        {
            try
            {
                var model = await _feedbackService.GetAdvancedFeedbackListAsync(
                    searchTerm, category, rating, status, priority, responseStatus, 
                    assignedTo, dateFrom, dateTo, escalatedOnly, sortBy, sortDirection);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving advanced feedback list");
                TempData["ErrorMessage"] = "An error occurred while retrieving feedback data.";
                return View(new FeedbackListViewModel());
            }
        }

        // GET: Management/Respond/{id}
        public async Task<IActionResult> Respond(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var feedback = await _feedbackService.GetFeedbackByIdAsync(id);
                if (feedback == null)
                {
                    return NotFound();
                }

                var model = new ResponseViewModel
                {
                    FeedbackId = id,
                    Feedback = feedback,
                    ExistingResponses = await _responseService.GetResponsesByFeedbackIdAsync(id)
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading response form for feedback {FeedbackId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading the response form.";
                return RedirectToAction(nameof(Feedbacks));
            }
        }

        // POST: Management/Respond
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Respond(ResponseViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await _responseService.CreateResponseAsync(model);
                    TempData["SuccessMessage"] = "Response sent successfully!";
                    return RedirectToAction(nameof(Feedbacks));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating response for feedback {FeedbackId}", model.FeedbackId);
                    ModelState.AddModelError("", "An error occurred while sending the response. Please try again.");
                }
            }

            // Reload feedback and responses if validation fails
            try
            {
                model.Feedback = await _feedbackService.GetFeedbackByIdAsync(model.FeedbackId);
                model.ExistingResponses = await _responseService.GetResponsesByFeedbackIdAsync(model.FeedbackId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reloading feedback data for response form");
            }

            return View(model);
        }

        // POST: Management/BulkAction
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BulkAction(BulkActionViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var result = await _responseService.ProcessBulkActionAsync(model);
                    
                    if (result.Errors.Any())
                    {
                        TempData["ErrorMessage"] = $"Bulk action completed with errors: {string.Join(", ", result.Errors)}";
                    }
                    else
                    {
                        TempData["SuccessMessage"] = $"Bulk action '{model.Action}' completed successfully!";
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing bulk action {Action}", model.Action);
                    TempData["ErrorMessage"] = "An error occurred while processing the bulk action.";
                }
            }
            else
            {
                TempData["ErrorMessage"] = "Invalid bulk action request.";
            }

            return RedirectToAction(nameof(Feedbacks));
        }

        // POST: Management/Escalate/{id}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Escalate(string id, string reason = "Manual escalation")
        {
            try
            {
                var success = await _responseService.EscalateFeedbackAsync(id, reason);
                if (success)
                {
                    TempData["SuccessMessage"] = "Feedback escalated successfully!";
                }
                else
                {
                    TempData["ErrorMessage"] = "Failed to escalate feedback.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error escalating feedback {FeedbackId}", id);
                TempData["ErrorMessage"] = "An error occurred while escalating the feedback.";
            }

            return RedirectToAction(nameof(Feedbacks));
        }

        // POST: Management/Assign/{id}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Assign(string id, string adminName)
        {
            try
            {
                var success = await _responseService.AssignFeedbackAsync(id, adminName);
                if (success)
                {
                    TempData["SuccessMessage"] = $"Feedback assigned to {adminName} successfully!";
                }
                else
                {
                    TempData["ErrorMessage"] = "Failed to assign feedback.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning feedback {FeedbackId} to {AdminName}", id, adminName);
                TempData["ErrorMessage"] = "An error occurred while assigning the feedback.";
            }

            return RedirectToAction(nameof(Feedbacks));
        }

        // GET: Management/EmailTemplates
        public IActionResult EmailTemplates()
        {
            var templates = _emailService.GetEmailTemplates();
            return View(templates);
        }

        // GET: Management/TestEmail
        public async Task<IActionResult> TestEmail()
        {
            try
            {
                var isConfigured = await _emailService.TestEmailConfigurationAsync();
                ViewBag.EmailConfigured = isConfigured;
                ViewBag.Message = isConfigured ? "Email configuration is working correctly!" : "Email configuration test failed. Please check your settings.";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing email configuration");
                ViewBag.EmailConfigured = false;
                ViewBag.Message = $"Email test failed: {ex.Message}";
            }

            return View();
        }

        // API endpoint for getting feedback requiring attention
        [HttpGet]
        public async Task<IActionResult> GetAttentionRequired()
        {
            try
            {
                var feedbacks = await _responseService.GetFeedbacksRequiringAttentionAsync();
                return Json(new { success = true, data = feedbacks, count = feedbacks.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feedbacks requiring attention");
                return Json(new { success = false, message = "An error occurred while retrieving attention data." });
            }
        }

        // API endpoint for quick status updates
        [HttpPost]
        public async Task<IActionResult> QuickStatusUpdate(string feedbackId, string newStatus)
        {
            try
            {
                var feedback = await _feedbackService.GetFeedbackByIdAsync(feedbackId);
                if (feedback != null)
                {
                    feedback.Status = newStatus;
                    await _feedbackService.UpdateFeedbackAsync(new EditFeedbackViewModel
                    {
                        Id = feedback.Id,
                        CustomerName = feedback.CustomerName,
                        Email = feedback.Email,
                        FeedbackText = feedback.FeedbackText,
                        Rating = feedback.Rating,
                        Category = feedback.Category,
                        Status = newStatus,
                        Timestamp = feedback.Timestamp
                    });

                    return Json(new { success = true, message = "Status updated successfully" });
                }
                return Json(new { success = false, message = "Feedback not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating feedback status");
                return Json(new { success = false, message = "An error occurred while updating status" });
            }
        }
    }
}
