using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using demoDynamoDB.Models;
using demoDynamoDB.Services;

namespace demoDynamoDB.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IFeedbackService _feedbackService;

    public HomeController(ILogger<HomeController> logger, IFeedbackService feedbackService)
    {
        _logger = logger;
        _feedbackService = feedbackService;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            var dashboard = await _feedbackService.GetDashboardAsync();
            return View(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard");
            TempData["ErrorMessage"] = "An error occurred while loading the dashboard.";
            return View(new DashboardViewModel());
        }
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
