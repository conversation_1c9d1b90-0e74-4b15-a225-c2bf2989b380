@model demoDynamoDB.Models.EditFeedbackViewModel
@{
    ViewData["Title"] = "Edit Feedback";
}

<div class="container feedback-form-container content-with-footer-spacing">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card feedback-card-container">
                <div class="card-header">
                    <h4><i class="fas fa-edit"></i> Edit Feedback</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="Timestamp" type="hidden" />
                        
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CustomerName" class="form-label">Customer Name *</label>
                                <input asp-for="CustomerName" class="form-control">
                                <span asp-validation-for="CustomerName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">Email Address *</label>
                                <input asp-for="Email" class="form-control" type="email">
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Category" class="form-label">Category</label>
                                <select asp-for="Category" class="form-select">
                                    @foreach (var category in Model.AvailableCategories)
                                    {
                                        <option value="@category">@category</option>
                                    }
                                </select>
                                <span asp-validation-for="Category" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Rating" class="form-label">Rating *</label>
                                <select asp-for="Rating" class="form-select">
                                    <option value="5">5 - Excellent ⭐⭐⭐⭐⭐</option>
                                    <option value="4">4 - Good ⭐⭐⭐⭐</option>
                                    <option value="3">3 - Average ⭐⭐⭐</option>
                                    <option value="2">2 - Poor ⭐⭐</option>
                                    <option value="1">1 - Very Poor ⭐</option>
                                </select>
                                <span asp-validation-for="Rating" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Status" class="form-label">Status</label>
                                <select asp-for="Status" class="form-select">
                                    @foreach (var status in Model.AvailableStatuses)
                                    {
                                        <option value="@status">@status</option>
                                    }
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="FeedbackText" class="form-label">Feedback Content *</label>
                            <textarea asp-for="FeedbackText" class="form-control" rows="6"></textarea>
                            <div class="form-text">Maximum 1000 characters</div>
                            <span asp-validation-for="FeedbackText" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Feedback Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <strong>Feedback ID:</strong> @Model.Id
                                            </small>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <strong>Submitted:</strong> @Model.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <div>
                                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Character counter for feedback text
        document.addEventListener('DOMContentLoaded', function() {
            const feedbackTextarea = document.getElementById('FeedbackText');
            const maxLength = 1000;
            
            if (feedbackTextarea) {
                const counterDiv = document.createElement('div');
                counterDiv.className = 'form-text text-end';
                feedbackTextarea.parentNode.appendChild(counterDiv);
                
                function updateCounter() {
                    const remaining = maxLength - feedbackTextarea.value.length;
                    counterDiv.textContent = `${feedbackTextarea.value.length}/${maxLength} characters`;
                    counterDiv.className = remaining < 100 ? 'form-text text-end text-warning' : 'form-text text-end';
                }
                
                feedbackTextarea.addEventListener('input', updateCounter);
                updateCounter();
            }
        });
    </script>
}
