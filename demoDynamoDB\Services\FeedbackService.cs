using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public class FeedbackService : IFeedbackService
    {
        private readonly IDynamoDBService _dynamoDBService;

        public FeedbackService(IDynamoDBService dynamoDBService)
        {
            _dynamoDBService = dynamoDBService;
        }

        public async Task<FeedbackListViewModel> GetFeedbackListAsync(string searchTerm = "", string category = "", int? rating = null, string status = "")
        {
            var feedbacks = await _dynamoDBService.SearchFeedbacksAsync(searchTerm, category, rating, status);
            var categories = await GetCategoriesAsync();
            var statuses = await GetStatusesAsync();

            return new FeedbackListViewModel
            {
                Feedbacks = feedbacks,
                SearchTerm = searchTerm,
                CategoryFilter = category,
                RatingFilter = rating,
                StatusFilter = status,
                TotalCount = feedbacks.Count,
                Categories = categories,
                Statuses = statuses
            };
        }

        public async Task<FeedbackListViewModel> GetAdvancedFeedbackListAsync(
            string searchTerm = "",
            string category = "",
            int? rating = null,
            string status = "",
            string priority = "",
            string responseStatus = "",
            string assignedTo = "",
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            bool escalatedOnly = false,
            string sortBy = "Timestamp",
            string sortDirection = "desc")
        {
            var feedbacks = await _dynamoDBService.SearchFeedbacksAdvancedAsync(
                searchTerm, category, rating, status, priority, responseStatus,
                assignedTo, dateFrom, dateTo, escalatedOnly, sortBy, sortDirection);

            var categories = await GetCategoriesAsync();
            var statuses = await GetStatusesAsync();
            var priorities = await GetPrioritiesAsync();
            var responseStatuses = await GetResponseStatusesAsync();
            var administrators = await GetAdministratorsAsync();

            return new FeedbackListViewModel
            {
                Feedbacks = feedbacks,
                SearchTerm = searchTerm,
                CategoryFilter = category,
                RatingFilter = rating,
                StatusFilter = status,
                PriorityFilter = priority,
                ResponseStatusFilter = responseStatus,
                AssignedToFilter = assignedTo,
                DateFromFilter = dateFrom,
                DateToFilter = dateTo,
                ShowEscalatedOnly = escalatedOnly,
                SortBy = sortBy,
                SortDirection = sortDirection,
                TotalCount = feedbacks.Count,
                Categories = categories,
                Statuses = statuses,
                Priorities = priorities,
                ResponseStatuses = responseStatuses,
                Administrators = administrators
            };
        }

        public async Task<CustomerFeedback?> GetFeedbackByIdAsync(string id)
        {
            return await _dynamoDBService.GetFeedbackByIdAsync(id);
        }

        public async Task<CustomerFeedback> CreateFeedbackAsync(CreateFeedbackViewModel model)
        {
            var feedback = new CustomerFeedback
            {
                CustomerName = model.CustomerName,
                Email = model.Email,
                FeedbackText = model.FeedbackText,
                Rating = model.Rating,
                Category = model.Category,
                Status = "New",
                Timestamp = DateTime.UtcNow
            };

            return await _dynamoDBService.CreateFeedbackAsync(feedback);
        }

        public async Task<CustomerFeedback> UpdateFeedbackAsync(EditFeedbackViewModel model)
        {
            var feedback = new CustomerFeedback
            {
                Id = model.Id,
                CustomerName = model.CustomerName,
                Email = model.Email,
                FeedbackText = model.FeedbackText,
                Rating = model.Rating,
                Category = model.Category,
                Status = model.Status,
                Timestamp = model.Timestamp
            };

            return await _dynamoDBService.UpdateFeedbackAsync(feedback);
        }

        public async Task<bool> DeleteFeedbackAsync(string id)
        {
            return await _dynamoDBService.DeleteFeedbackAsync(id);
        }

        public async Task<DashboardViewModel> GetDashboardAsync()
        {
            return await _dynamoDBService.GetDashboardStatsAsync();
        }

        public async Task<List<string>> GetCategoriesAsync()
        {
            var feedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var categories = feedbacks.Select(f => f.Category).Distinct().OrderBy(c => c).ToList();
            
            // Add default categories if they don't exist
            var defaultCategories = new[] { "General", "Product Quality", "Customer Service", "Delivery", "Website", "Pricing", "Other" };
            foreach (var category in defaultCategories)
            {
                if (!categories.Contains(category))
                {
                    categories.Add(category);
                }
            }
            
            return categories.OrderBy(c => c).ToList();
        }

        public async Task<List<string>> GetStatusesAsync()
        {
            var feedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var statuses = feedbacks.Select(f => f.Status).Distinct().OrderBy(s => s).ToList();

            // Add default statuses if they don't exist
            var defaultStatuses = new[] { "New", "In Progress", "Resolved", "Closed" };
            foreach (var status in defaultStatuses)
            {
                if (!statuses.Contains(status))
                {
                    statuses.Add(status);
                }
            }

            return statuses.OrderBy(s => s).ToList();
        }

        public async Task<List<string>> GetPrioritiesAsync()
        {
            var feedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var priorities = feedbacks.Select(f => f.Priority).Distinct().OrderBy(p => p).ToList();

            // Add default priorities if they don't exist
            var defaultPriorities = new[] { "High", "Medium", "Low" };
            foreach (var priority in defaultPriorities)
            {
                if (!priorities.Contains(priority))
                {
                    priorities.Add(priority);
                }
            }

            return new[] { "High", "Medium", "Low" }.ToList(); // Return in priority order
        }

        public async Task<List<string>> GetResponseStatusesAsync()
        {
            var feedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var responseStatuses = feedbacks.Select(f => f.ResponseStatus).Distinct().OrderBy(rs => rs).ToList();

            // Add default response statuses if they don't exist
            var defaultResponseStatuses = new[] { "Pending Response", "Responded", "Follow-up Required", "Escalated" };
            foreach (var responseStatus in defaultResponseStatuses)
            {
                if (!responseStatuses.Contains(responseStatus))
                {
                    responseStatuses.Add(responseStatus);
                }
            }

            return responseStatuses.OrderBy(rs => rs).ToList();
        }

        public async Task<List<string>> GetAdministratorsAsync()
        {
            var feedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var administrators = feedbacks
                .Where(f => !string.IsNullOrEmpty(f.AssignedTo))
                .Select(f => f.AssignedTo)
                .Distinct()
                .OrderBy(a => a)
                .ToList();

            // Add default administrators if they don't exist
            var defaultAdministrators = new[] { "Admin1", "Admin2", "Admin3" };
            foreach (var admin in defaultAdministrators)
            {
                if (!administrators.Contains(admin))
                {
                    administrators.Add(admin);
                }
            }

            administrators.Add("Unassigned");
            return administrators.OrderBy(a => a).ToList();
        }
    }
}
