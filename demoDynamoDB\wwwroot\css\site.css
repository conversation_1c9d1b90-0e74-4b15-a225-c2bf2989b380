html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus,
.btn:active:focus,
.btn-link.nav-link:focus,
.form-control:focus,
.form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

/* Custom styles for Customer Feedback System */
.card {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.navbar-brand {
  font-weight: bold;
}

.feedback-card {
  border-left: 4px solid #007bff;
}

.feedback-card.rating-5 {
  border-left-color: #28a745;
}

.feedback-card.rating-4 {
  border-left-color: #20c997;
}

.feedback-card.rating-3 {
  border-left-color: #ffc107;
}

.feedback-card.rating-2 {
  border-left-color: #fd7e14;
}

.feedback-card.rating-1 {
  border-left-color: #dc3545;
}

.status-new {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.status-in-progress {
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
}

.status-resolved {
  background-color: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.status-closed {
  background-color: #f5f5f5;
  border-left: 4px solid #9e9e9e;
}

.dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.dashboard-card .card-body {
  padding: 1.5rem;
}

.stats-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.progress {
  height: 8px;
  border-radius: 4px;
}

.rating-stars {
  color: #ffc107;
  font-size: 1.2em;
}

.feedback-text-preview {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.alert {
  border-radius: 8px;
}

.form-control,
.form-select {
  border-radius: 6px;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
}

.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer {
  background-color: #f8f9fa;
  padding: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-columns {
    column-count: 1;
  }

  .btn-group {
    display: flex;
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }
}

/* Animation for loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-floating > .form-control-plaintext::placeholder,
.form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder,
.form-floating > .form-control:focus::placeholder {
  text-align: start;
}
