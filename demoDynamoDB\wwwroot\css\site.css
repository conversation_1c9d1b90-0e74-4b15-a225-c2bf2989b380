html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus,
.btn:active:focus,
.btn-link.nav-link:focus,
.form-control:focus,
.form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header styling */
header {
  flex-shrink: 0;
}

/* Main content area styling - this will grow to fill available space */
.main-content-wrapper {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
}

.container-fluid {
  flex: 1;
  padding-bottom: 2rem; /* Additional bottom padding for content */
}

main {
  flex: 1;
  padding-bottom: 1rem; /* Reduced padding since we're using proper flexbox */
}

/* Specific styling for feedback forms and detail pages */
.feedback-form-container,
.feedback-details-container {
  margin-bottom: 3rem; /* Extra margin for form pages */
}

/* Ensure cards in feedback views have proper spacing */
.feedback-card-container {
  margin-bottom: 2rem;
}

/* Additional bottom spacing for long content pages */
.content-with-footer-spacing {
  padding-bottom: 4rem;
}

/* Custom styles for Customer Feedback System */
.card {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.navbar-brand {
  font-weight: bold;
}

.feedback-card {
  border-left: 4px solid #007bff;
}

.feedback-card.rating-5 {
  border-left-color: #28a745;
}

.feedback-card.rating-4 {
  border-left-color: #20c997;
}

.feedback-card.rating-3 {
  border-left-color: #ffc107;
}

.feedback-card.rating-2 {
  border-left-color: #fd7e14;
}

.feedback-card.rating-1 {
  border-left-color: #dc3545;
}

.status-new {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.status-in-progress {
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
}

.status-resolved {
  background-color: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.status-closed {
  background-color: #f5f5f5;
  border-left: 4px solid #9e9e9e;
}

/* Dashboard Cards Styling */
.dashboard-cards-row {
  display: flex;
  flex-wrap: wrap;
}

.dashboard-summary-card {
  height: 120px;
  min-height: 120px;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.dashboard-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-summary-card .card-body {
  padding: 1.25rem;
  height: 100%;
  display: flex;
  align-items: center;
}

.dashboard-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 70px;
}

.dashboard-card-content h4 {
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.25rem !important;
}

.dashboard-card-label {
  font-size: 0.875rem;
  opacity: 0.9;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 0 !important;
}

.dashboard-card-extra {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.2;
  min-height: 1rem;
  margin-top: 0.125rem;
}

.dashboard-card-icon {
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;
}

.dashboard-card-icon i {
  font-size: 2rem !important;
}

.dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.dashboard-card .card-body {
  padding: 1.5rem;
}

.stats-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.progress {
  height: 8px;
  border-radius: 4px;
}

.rating-stars {
  color: #ffc107;
  font-size: 1.2em;
}

.feedback-text-preview {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.alert {
  border-radius: 8px;
}

.form-control,
.form-select {
  border-radius: 6px;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
}

.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer {
  background-color: #f8f9fa;
  padding: 1.5rem 0;
  border-top: 1px solid #dee2e6;
  flex-shrink: 0;
  margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-columns {
    column-count: 1;
  }

  .btn-group {
    display: flex;
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }

  /* Dashboard cards responsive */
  .dashboard-summary-card {
    height: auto;
    min-height: 100px;
  }

  .dashboard-card-content h4 {
    font-size: 1.5rem;
  }

  .dashboard-card-icon i {
    font-size: 1.5rem !important;
  }

  .dashboard-card-icon {
    margin-left: 0.5rem;
  }
}

@media (max-width: 576px) {
  .dashboard-summary-card .card-body {
    padding: 1rem;
  }

  .dashboard-card-content {
    min-height: 60px;
  }

  .dashboard-card-content h4 {
    font-size: 1.25rem;
  }

  .dashboard-card-label {
    font-size: 0.8rem;
  }

  .dashboard-card-extra {
    font-size: 0.7rem;
  }

  /* Footer and content spacing adjustments for mobile */
  .footer {
    padding: 1rem 0;
  }

  .container-fluid {
    padding-bottom: 2rem; /* Bottom padding on mobile */
  }

  main {
    padding-bottom: 2rem !important;
  }

  .feedback-form-container,
  .feedback-details-container {
    margin-bottom: 3rem; /* Extra margin for mobile form pages */
  }
}

/* Extra small screens adjustments */
@media (max-width: 480px) {
  .footer {
    padding: 0.75rem 0;
  }

  .container-fluid {
    padding-bottom: 3rem;
  }

  main {
    padding-bottom: 3rem !important;
  }

  .feedback-form-container,
  .feedback-details-container {
    margin-bottom: 4rem; /* Maximum margin for very small screens */
  }

  .content-with-footer-spacing {
    padding-bottom: 4rem; /* Maximum bottom spacing */
  }
}

/* Animation for loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-floating > .form-control-plaintext::placeholder,
.form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder,
.form-floating > .form-control:focus::placeholder {
  text-align: start;
}
