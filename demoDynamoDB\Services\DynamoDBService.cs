using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.Model;
using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public class DynamoDBService : IDynamoDBService
    {
        private readonly IAmazonDynamoDB _dynamoDBClient;
        private readonly DynamoDBContext _dynamoDBContext;
        private readonly IConfiguration _configuration;
        private readonly string _tableName;

        public DynamoDBService(IAmazonDynamoDB dynamoDBClient, DynamoDBContext dynamoDBContext, IConfiguration configuration)
        {
            _dynamoDBClient = dynamoDBClient;
            _dynamoDBContext = dynamoDBContext;
            _configuration = configuration;
            _tableName = _configuration["DynamoDB:TableName"] ?? "CustomerFeedback";
        }

        public async Task InitializeTableAsync()
        {
            if (!await TableExistsAsync())
            {
                await CreateTableAsync();
                await SeedDataAsync();
            }
        }

        public async Task<bool> TableExistsAsync()
        {
            try
            {
                var response = await _dynamoDBClient.DescribeTableAsync(_tableName);
                return response.Table.TableStatus == TableStatus.ACTIVE;
            }
            catch (ResourceNotFoundException)
            {
                return false;
            }
        }

        public async Task CreateTableAsync()
        {
            var request = new CreateTableRequest
            {
                TableName = _tableName,
                KeySchema = new List<KeySchemaElement>
                {
                    new KeySchemaElement
                    {
                        AttributeName = "Id",
                        KeyType = KeyType.HASH
                    }
                },
                AttributeDefinitions = new List<AttributeDefinition>
                {
                    new AttributeDefinition
                    {
                        AttributeName = "Id",
                        AttributeType = ScalarAttributeType.S
                    }
                },
                BillingMode = BillingMode.PAY_PER_REQUEST
            };

            await _dynamoDBClient.CreateTableAsync(request);

            // Wait for table to be active
            var tableStatus = TableStatus.CREATING;
            while (tableStatus == TableStatus.CREATING)
            {
                await Task.Delay(2000);
                var response = await _dynamoDBClient.DescribeTableAsync(_tableName);
                tableStatus = response.Table.TableStatus;
            }
        }

        public async Task SeedDataAsync()
        {
            var sampleFeedbacks = new List<CustomerFeedback>
            {
                new CustomerFeedback
                {
                    CustomerName = "John Smith",
                    Email = "<EMAIL>",
                    FeedbackText = "Great product quality and fast delivery. Very satisfied with my purchase!",
                    Rating = 5,
                    Category = "Product Quality",
                    Status = "Resolved",
                    Priority = "Medium",
                    AssignedTo = "Admin1",
                    ResponseStatus = "Responded",
                    LastResponseDate = DateTime.UtcNow.AddDays(-4),
                    EmailStatus = "Sent",
                    Timestamp = DateTime.UtcNow.AddDays(-5)
                },
                new CustomerFeedback
                {
                    CustomerName = "Sarah Johnson",
                    Email = "<EMAIL>",
                    FeedbackText = "Customer service was helpful, but the website could be more user-friendly.",
                    Rating = 4,
                    Category = "Customer Service",
                    Status = "In Progress",
                    Priority = "Medium",
                    AssignedTo = "Admin2",
                    ResponseStatus = "Follow-up Required",
                    LastResponseDate = DateTime.UtcNow.AddDays(-2),
                    EmailStatus = "Sent",
                    Timestamp = DateTime.UtcNow.AddDays(-3)
                },
                new CustomerFeedback
                {
                    CustomerName = "Mike Wilson",
                    Email = "<EMAIL>",
                    FeedbackText = "Delivery was delayed and the package was damaged. Not happy with this experience.",
                    Rating = 2,
                    Category = "Delivery",
                    Status = "New",
                    Priority = "High",
                    AssignedTo = "Admin1",
                    ResponseStatus = "Pending Response",
                    EmailStatus = "Not Sent",
                    IsEscalated = true,
                    EscalatedDate = DateTime.UtcNow.AddHours(-12),
                    InternalNotes = "Customer very upset - needs immediate attention",
                    Timestamp = DateTime.UtcNow.AddDays(-1)
                },
                new CustomerFeedback
                {
                    CustomerName = "Emily Davis",
                    Email = "<EMAIL>",
                    FeedbackText = "Love the new features on the website! Easy to navigate and find what I need.",
                    Rating = 5,
                    Category = "Website",
                    Status = "Closed",
                    Priority = "Low",
                    AssignedTo = "Admin3",
                    ResponseStatus = "Responded",
                    LastResponseDate = DateTime.UtcNow.AddDays(-6),
                    EmailStatus = "Sent",
                    Timestamp = DateTime.UtcNow.AddDays(-7)
                },
                new CustomerFeedback
                {
                    CustomerName = "Robert Brown",
                    Email = "<EMAIL>",
                    FeedbackText = "Pricing is reasonable and the quality matches the cost. Good value for money.",
                    Rating = 4,
                    Category = "Pricing",
                    Status = "Resolved",
                    Priority = "Low",
                    AssignedTo = "Admin2",
                    ResponseStatus = "Responded",
                    LastResponseDate = DateTime.UtcNow.AddDays(-1),
                    EmailStatus = "Sent",
                    Timestamp = DateTime.UtcNow.AddDays(-2)
                },
                new CustomerFeedback
                {
                    CustomerName = "Lisa Chen",
                    Email = "<EMAIL>",
                    FeedbackText = "The checkout process is confusing and took me several attempts to complete my order.",
                    Rating = 3,
                    Category = "Website",
                    Status = "New",
                    Priority = "High",
                    AssignedTo = "",
                    ResponseStatus = "Pending Response",
                    EmailStatus = "Not Sent",
                    Timestamp = DateTime.UtcNow.AddHours(-6)
                },
                new CustomerFeedback
                {
                    CustomerName = "David Martinez",
                    Email = "<EMAIL>",
                    FeedbackText = "Excellent customer support! The team resolved my issue quickly and professionally.",
                    Rating = 5,
                    Category = "Customer Service",
                    Status = "Closed",
                    Priority = "Low",
                    AssignedTo = "Admin1",
                    ResponseStatus = "Responded",
                    LastResponseDate = DateTime.UtcNow.AddHours(-18),
                    EmailStatus = "Sent",
                    Timestamp = DateTime.UtcNow.AddDays(-1)
                }
            };

            foreach (var feedback in sampleFeedbacks)
            {
                await _dynamoDBContext.SaveAsync(feedback);
            }
        }

        public async Task<CustomerFeedback?> GetFeedbackByIdAsync(string id)
        {
            return await _dynamoDBContext.LoadAsync<CustomerFeedback>(id);
        }

        public async Task<List<CustomerFeedback>> GetAllFeedbacksAsync()
        {
            var scanConditions = new List<ScanCondition>();
            var search = _dynamoDBContext.ScanAsync<CustomerFeedback>(scanConditions);
            var feedbacks = await search.GetRemainingAsync();
            return feedbacks.OrderByDescending(f => f.Timestamp).ToList();
        }

        public async Task<CustomerFeedback> CreateFeedbackAsync(CustomerFeedback feedback)
        {
            feedback.Id = Guid.NewGuid().ToString();
            feedback.Timestamp = DateTime.UtcNow;
            await _dynamoDBContext.SaveAsync(feedback);
            return feedback;
        }

        public async Task<CustomerFeedback> UpdateFeedbackAsync(CustomerFeedback feedback)
        {
            await _dynamoDBContext.SaveAsync(feedback);
            return feedback;
        }

        public async Task<bool> DeleteFeedbackAsync(string id)
        {
            try
            {
                await _dynamoDBContext.DeleteAsync<CustomerFeedback>(id);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<CustomerFeedback>> SearchFeedbacksAsync(string searchTerm, string? category = null, int? rating = null, string? status = null)
        {
            var allFeedbacks = await GetAllFeedbacksAsync();

            var filteredFeedbacks = allFeedbacks.Where(f =>
                (string.IsNullOrEmpty(searchTerm) ||
                 f.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 f.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 f.FeedbackText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(category) || f.Category.Equals(category, StringComparison.OrdinalIgnoreCase)) &&
                (!rating.HasValue || f.Rating == rating.Value) &&
                (string.IsNullOrEmpty(status) || f.Status.Equals(status, StringComparison.OrdinalIgnoreCase))
            ).ToList();

            return filteredFeedbacks;
        }

        public async Task<List<CustomerFeedback>> SearchFeedbacksAdvancedAsync(
            string searchTerm = "",
            string? category = null,
            int? rating = null,
            string? status = null,
            string? priority = null,
            string? responseStatus = null,
            string? assignedTo = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            bool escalatedOnly = false,
            string sortBy = "Timestamp",
            string sortDirection = "desc")
        {
            var allFeedbacks = await GetAllFeedbacksAsync();

            var filteredFeedbacks = allFeedbacks.Where(f =>
                (string.IsNullOrEmpty(searchTerm) ||
                 f.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 f.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 f.FeedbackText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 f.InternalNotes.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(category) || f.Category.Equals(category, StringComparison.OrdinalIgnoreCase)) &&
                (!rating.HasValue || f.Rating == rating.Value) &&
                (string.IsNullOrEmpty(status) || f.Status.Equals(status, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(priority) || f.Priority.Equals(priority, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(responseStatus) || f.ResponseStatus.Equals(responseStatus, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(assignedTo) || f.AssignedTo.Equals(assignedTo, StringComparison.OrdinalIgnoreCase)) &&
                (!dateFrom.HasValue || f.Timestamp >= dateFrom.Value) &&
                (!dateTo.HasValue || f.Timestamp <= dateTo.Value) &&
                (!escalatedOnly || f.IsEscalated)
            );

            // Apply sorting
            filteredFeedbacks = sortBy.ToLower() switch
            {
                "timestamp" => sortDirection == "asc" ?
                    filteredFeedbacks.OrderBy(f => f.Timestamp) :
                    filteredFeedbacks.OrderByDescending(f => f.Timestamp),
                "rating" => sortDirection == "asc" ?
                    filteredFeedbacks.OrderBy(f => f.Rating) :
                    filteredFeedbacks.OrderByDescending(f => f.Rating),
                "priority" => sortDirection == "asc" ?
                    filteredFeedbacks.OrderBy(f => f.Priority) :
                    filteredFeedbacks.OrderByDescending(f => f.Priority),
                "customername" => sortDirection == "asc" ?
                    filteredFeedbacks.OrderBy(f => f.CustomerName) :
                    filteredFeedbacks.OrderByDescending(f => f.CustomerName),
                "category" => sortDirection == "asc" ?
                    filteredFeedbacks.OrderBy(f => f.Category) :
                    filteredFeedbacks.OrderByDescending(f => f.Category),
                "status" => sortDirection == "asc" ?
                    filteredFeedbacks.OrderBy(f => f.Status) :
                    filteredFeedbacks.OrderByDescending(f => f.Status),
                _ => filteredFeedbacks.OrderByDescending(f => f.Timestamp)
            };

            return filteredFeedbacks.ToList();
        }

        public async Task<DashboardViewModel> GetDashboardStatsAsync()
        {
            var allFeedbacks = await GetAllFeedbacksAsync();

            var dashboard = new DashboardViewModel
            {
                TotalFeedbacks = allFeedbacks.Count,
                AverageRating = allFeedbacks.Any() ? allFeedbacks.Average(f => f.Rating) : 0,
                NewFeedbacks = allFeedbacks.Count(f => f.Status == "New"),
                InProgressFeedbacks = allFeedbacks.Count(f => f.Status == "In Progress"),
                ResolvedFeedbacks = allFeedbacks.Count(f => f.Status == "Resolved"),
                ClosedFeedbacks = allFeedbacks.Count(f => f.Status == "Closed"),
                RecentFeedbacks = allFeedbacks.Take(5).ToList()
            };

            // Category statistics
            dashboard.CategoryStats = allFeedbacks
                .GroupBy(f => f.Category)
                .Select(g => new CategoryStats
                {
                    Category = g.Key,
                    Count = g.Count(),
                    AverageRating = g.Average(f => f.Rating)
                })
                .OrderByDescending(c => c.Count)
                .ToList();

            // Rating statistics
            dashboard.RatingStats = allFeedbacks
                .GroupBy(f => f.Rating)
                .Select(g => new RatingStats
                {
                    Rating = g.Key,
                    Count = g.Count(),
                    Percentage = allFeedbacks.Any() ? (double)g.Count() / allFeedbacks.Count * 100 : 0
                })
                .OrderByDescending(r => r.Rating)
                .ToList();

            return dashboard;
        }
    }
}
