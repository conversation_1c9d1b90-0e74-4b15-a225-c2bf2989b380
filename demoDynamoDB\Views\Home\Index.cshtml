﻿@model demoDynamoDB.Models.DashboardViewModel
@{
    ViewData["Title"] = "Customer Feedback Dashboard";
}

<div class="container-fluid">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-tachometer-alt"></i> Customer Feedback Dashboard</h2>
        <a asp-controller="Feedback" asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Feedback
        </a>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4 dashboard-cards-row">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white dashboard-summary-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center h-100">
                        <div class="dashboard-card-content">
                            <h4 class="mb-1">@Model.TotalFeedbacks</h4>
                            <p class="mb-0 dashboard-card-label">Total Feedback</p>
                            <div class="dashboard-card-extra">&nbsp;</div>
                        </div>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-comments fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white dashboard-summary-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center h-100">
                        <div class="dashboard-card-content">
                            <h4 class="mb-1">@Model.FormattedAverageRating</h4>
                            <p class="mb-0 dashboard-card-label">Average Rating</p>
                            <div class="dashboard-card-extra">@Model.AverageRatingStars</div>
                        </div>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white dashboard-summary-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center h-100">
                        <div class="dashboard-card-content">
                            <h4 class="mb-1">@Model.NewFeedbacks</h4>
                            <p class="mb-0 dashboard-card-label">New Feedback</p>
                            <div class="dashboard-card-extra">&nbsp;</div>
                        </div>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white dashboard-summary-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center h-100">
                        <div class="dashboard-card-content">
                            <h4 class="mb-1">@Model.InProgressFeedbacks</h4>
                            <p class="mb-0 dashboard-card-label">In Progress</p>
                            <div class="dashboard-card-extra">&nbsp;</div>
                        </div>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-cog fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Overview -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> Status Distribution</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-primary">New</span>
                                <strong>@Model.NewFeedbacks</strong>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-warning">In Progress</span>
                                <strong>@Model.InProgressFeedbacks</strong>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-success">Resolved</span>
                                <strong>@Model.ResolvedFeedbacks</strong>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-secondary">Closed</span>
                                <strong>@Model.ClosedFeedbacks</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-star"></i> Rating Distribution</h5>
                </div>
                <div class="card-body">
                    @foreach (var rating in Model.RatingStats)
                    {
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>@rating.Rating ⭐</span>
                            <div class="flex-grow-1 mx-3">
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" style="width: @rating.Percentage%">
                                        @rating.FormattedPercentage%
                                    </div>
                                </div>
                            </div>
                            <strong>@rating.Count</strong>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Category Statistics and Recent Feedback -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tags"></i> Category Statistics</h5>
                </div>
                <div class="card-body">
                    @if (Model.CategoryStats.Any())
                    {
                        @foreach (var category in Model.CategoryStats)
                        {
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <strong>@category.Category</strong>
                                    <br>
                                    <small class="text-muted">Avg: @category.FormattedAverageRating ⭐</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">@category.Count</span>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted text-center">No feedback categories available</p>
                    }
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-clock"></i> Recent Feedback</h5>
                    <a asp-controller="Feedback" asp-action="Index" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if (Model.RecentFeedbacks.Any())
                    {
                        @foreach (var feedback in Model.RecentFeedbacks)
                        {
                            <div class="border-bottom pb-2 mb-2">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <strong>@feedback.CustomerName</strong>
                                        <span class="ms-2">@feedback.RatingStars</span>
                                        <br>
                                        <small class="text-muted">
                                            @(feedback.FeedbackText.Length > 60 ? feedback.FeedbackText.Substring(0, 60) + "..." : feedback.FeedbackText)
                                        </small>
                                        <br>
                                        <small class="text-muted">@feedback.FormattedTimestamp</small>
                                    </div>
                                    <span class="badge bg-@GetStatusBadgeClass(feedback.Status) ms-2">@feedback.Status</span>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted text-center">No recent feedback available</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a asp-controller="Feedback" asp-action="Index" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list"></i> View All Feedback
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a asp-controller="Feedback" asp-action="Index" asp-route-status="New" class="btn btn-outline-warning w-100">
                                <i class="fas fa-bell"></i> New Feedback (@Model.NewFeedbacks)
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a asp-controller="Feedback" asp-action="Index" asp-route-rating="1" class="btn btn-outline-danger w-100">
                                <i class="fas fa-exclamation-triangle"></i> Low Ratings
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a asp-controller="Feedback" asp-action="Create" class="btn btn-primary w-100">
                                <i class="fas fa-plus"></i> Add Feedback
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "New" => "primary",
            "In Progress" => "warning",
            "Resolved" => "success",
            "Closed" => "secondary",
            _ => "light"
        };
    }
}