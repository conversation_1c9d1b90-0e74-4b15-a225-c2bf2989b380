# Customer Feedback Management System

A comprehensive web application for managing customer feedback using ASP.NET Core MVC 9.0 and DynamoDB Local.

## Features

### Core Functionality
- **Dashboard**: Real-time statistics and overview of customer feedback
- **CRUD Operations**: Create, Read, Update, and Delete customer feedback entries
- **Search & Filter**: Advanced filtering by category, rating, status, and text search
- **Status Management**: Track feedback through different stages (New, In Progress, Resolved, Closed)
- **Rating System**: 5-star rating system with visual indicators

### Technical Features
- **DynamoDB Local Integration**: Uses local DynamoDB instance (no AWS cloud required)
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5
- **Real-time Validation**: Client-side and server-side validation
- **Error Handling**: Comprehensive error handling and user feedback
- **Seed Data**: Automatic sample data generation for demonstration

## Prerequisites

- .NET 9.0 SDK
- DynamoDB Local running on localhost:8000
- DynamoDB Admin (optional) running on localhost:8001

## Installation & Setup

### 1. <PERSON>lone and Setup Project
```bash
# Navigate to project directory
cd demoDynamoDB

# Restore NuGet packages
dotnet restore

# Build the project
dotnet build
```

### 2. DynamoDB Local Setup
Ensure DynamoDB Local is running on localhost:8000. The application will automatically:
- Create the `CustomerFeedback` table if it doesn't exist
- Seed sample data for demonstration

### 3. Run the Application
```bash
dotnet run
```

The application will be available at:
- **HTTPS**: https://localhost:7000
- **HTTP**: http://localhost:5000

## Application Structure

### Models
- **CustomerFeedback**: Main entity for storing feedback data
- **FeedbackViewModel**: View models for different operations
- **DashboardViewModel**: Dashboard statistics and data

### Services
- **IDynamoDBService**: Interface for DynamoDB operations
- **DynamoDBService**: Implementation of DynamoDB operations
- **IFeedbackService**: Business logic interface
- **FeedbackService**: Business logic implementation

### Controllers
- **HomeController**: Dashboard and main navigation
- **FeedbackController**: CRUD operations for feedback management

### Views
- **Dashboard**: Overview with statistics and recent feedback
- **Feedback List**: Paginated list with search and filtering
- **Create/Edit Forms**: User-friendly forms for feedback management
- **Details View**: Comprehensive feedback information display

## Database Schema

### CustomerFeedback Table
```
Primary Key: Id (String)
Attributes:
- CustomerName (String): Customer's full name
- Email (String): Customer's email address
- FeedbackText (String): Detailed feedback content
- Rating (Number): Rating from 1-5
- Category (String): Feedback category
- Status (String): Current status (New, In Progress, Resolved, Closed)
- Timestamp (String): When feedback was submitted
```

## Configuration

### appsettings.json
```json
{
  "DynamoDB": {
    "LocalServiceUrl": "http://localhost:8000",
    "TableName": "CustomerFeedback"
  },
  "AWS": {
    "Profile": "default",
    "Region": "us-east-1"
  }
}
```

## Usage Guide

### Dashboard
- View overall statistics and metrics
- Quick access to recent feedback
- Status distribution charts
- Category-wise analytics

### Managing Feedback
1. **Create**: Use "Add New Feedback" to submit new feedback
2. **View**: Browse all feedback with filtering options
3. **Edit**: Update feedback details and status
4. **Delete**: Remove feedback entries (with confirmation)

### Search & Filtering
- **Text Search**: Search across customer name, email, and feedback content
- **Category Filter**: Filter by feedback categories
- **Rating Filter**: Filter by specific ratings
- **Status Filter**: Filter by current status

## API Endpoints

### Web Routes
- `GET /` - Dashboard
- `GET /Feedback` - Feedback list with filtering
- `GET /Feedback/Create` - Create feedback form
- `POST /Feedback/Create` - Submit new feedback
- `GET /Feedback/Details/{id}` - View feedback details
- `GET /Feedback/Edit/{id}` - Edit feedback form
- `POST /Feedback/Edit/{id}` - Update feedback
- `GET /Feedback/Delete/{id}` - Delete confirmation
- `POST /Feedback/Delete/{id}` - Delete feedback

### AJAX Endpoints
- `GET /Feedback/GetFeedbackData` - JSON data for dynamic loading

## Development Notes

### Adding New Categories
Categories are managed dynamically. New categories can be added through the feedback forms and will automatically appear in filter options.

### Status Workflow
The typical feedback workflow:
1. **New** - Newly submitted feedback
2. **In Progress** - Being reviewed/processed
3. **Resolved** - Issue addressed
4. **Closed** - Feedback completed

### Customization
- Modify `Models/CustomerFeedback.cs` to add new fields
- Update views in `Views/Feedback/` for UI changes
- Extend services for additional business logic

## Troubleshooting

### Common Issues

1. **DynamoDB Connection Error**
   - Ensure DynamoDB Local is running on localhost:8000
   - Check firewall settings

2. **Table Creation Issues**
   - Verify DynamoDB Local has write permissions
   - Check application logs for detailed error messages

3. **Package Restore Issues**
   - Run `dotnet clean` followed by `dotnet restore`
   - Ensure .NET 9.0 SDK is properly installed

### Logs
Application logs are available in the console output when running in development mode.

## External Dependencies

- **AWSSDK.DynamoDBv2**: AWS SDK for DynamoDB operations
- **AWSSDK.Extensions.NETCore.Setup**: AWS configuration extensions
- **Bootstrap 5**: UI framework
- **Font Awesome 6**: Icons
- **jQuery**: JavaScript utilities

## License

This project is for demonstration purposes and showcases DynamoDB Local integration with ASP.NET Core MVC.
