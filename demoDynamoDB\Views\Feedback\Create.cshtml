@model demoDynamoDB.Models.CreateFeedbackViewModel
@{
    ViewData["Title"] = "Submit Feedback";
}

<div class="container feedback-form-container content-with-footer-spacing">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card feedback-card-container">
                <div class="card-header">
                    <h4><i class="fas fa-comment-alt"></i> Submit Your Feedback</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CustomerName" class="form-label">Customer Name *</label>
                                <input asp-for="CustomerName" class="form-control" placeholder="Enter your full name">
                                <span asp-validation-for="CustomerName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">Email Address *</label>
                                <input asp-for="Email" class="form-control" type="email" placeholder="Enter your email">
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Category" class="form-label">Category</label>
                                <select asp-for="Category" class="form-select">
                                    @foreach (var category in Model.AvailableCategories)
                                    {
                                        <option value="@category">@category</option>
                                    }
                                </select>
                                <span asp-validation-for="Category" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Rating" class="form-label">Rating *</label>
                                <select asp-for="Rating" class="form-select">
                                    <option value="5">5 - Excellent ⭐⭐⭐⭐⭐</option>
                                    <option value="4">4 - Good ⭐⭐⭐⭐</option>
                                    <option value="3">3 - Average ⭐⭐⭐</option>
                                    <option value="2">2 - Poor ⭐⭐</option>
                                    <option value="1">1 - Very Poor ⭐</option>
                                </select>
                                <span asp-validation-for="Rating" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="FeedbackText" class="form-label">Your Feedback *</label>
                            <textarea asp-for="FeedbackText" class="form-control" rows="6" 
                                      placeholder="Please share your detailed feedback here..."></textarea>
                            <div class="form-text">Maximum 1000 characters</div>
                            <span asp-validation-for="FeedbackText" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Submit Feedback
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Character counter for feedback text
        document.addEventListener('DOMContentLoaded', function() {
            const feedbackTextarea = document.getElementById('FeedbackText');
            const maxLength = 1000;
            
            if (feedbackTextarea) {
                const counterDiv = document.createElement('div');
                counterDiv.className = 'form-text text-end';
                feedbackTextarea.parentNode.appendChild(counterDiv);
                
                function updateCounter() {
                    const remaining = maxLength - feedbackTextarea.value.length;
                    counterDiv.textContent = `${feedbackTextarea.value.length}/${maxLength} characters`;
                    counterDiv.className = remaining < 100 ? 'form-text text-end text-warning' : 'form-text text-end';
                }
                
                feedbackTextarea.addEventListener('input', updateCounter);
                updateCounter();
            }
        });
    </script>
}
