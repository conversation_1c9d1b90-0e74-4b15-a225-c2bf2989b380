using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public interface IDynamoDBService
    {
        Task InitializeTableAsync();
        Task<bool> TableExistsAsync();
        Task CreateTableAsync();
        Task SeedDataAsync();
        Task<CustomerFeedback?> GetFeedbackByIdAsync(string id);
        Task<List<CustomerFeedback>> GetAllFeedbacksAsync();
        Task<CustomerFeedback> CreateFeedbackAsync(CustomerFeedback feedback);
        Task<CustomerFeedback> UpdateFeedbackAsync(CustomerFeedback feedback);
        Task<bool> DeleteFeedbackAsync(string id);
        Task<List<CustomerFeedback>> SearchFeedbacksAsync(string searchTerm, string? category = null, int? rating = null, string? status = null);
        Task<DashboardViewModel> GetDashboardStatsAsync();
    }
}
