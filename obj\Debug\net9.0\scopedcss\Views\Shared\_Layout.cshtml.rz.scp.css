/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-q2o1oiyhau] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-q2o1oiyhau] {
  color: #0077cc;
}

.btn-primary[b-q2o1oiyhau] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-q2o1oiyhau], .nav-pills .show > .nav-link[b-q2o1oiyhau] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-q2o1oiyhau] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-q2o1oiyhau] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-q2o1oiyhau] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-q2o1oiyhau] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-q2o1oiyhau] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
