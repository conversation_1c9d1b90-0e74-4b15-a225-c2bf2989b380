@model demoDynamoDB.Models.FeedbackListViewModel
@{
    ViewData["Title"] = "Customer Feedback";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-comments"></i> Customer Feedback</h2>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Feedback
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" asp-action="Index" class="row g-3">
                        <div class="col-md-3">
                            <label for="searchTerm" class="form-label">Search</label>
                            <input type="text" class="form-control" id="searchTerm" name="searchTerm" 
                                   value="@Model.SearchTerm" placeholder="Search by name, email, or feedback...">
                        </div>
                        <div class="col-md-2">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                @foreach (var cat in Model.Categories)
                                {
                                    <option value="@cat" selected="@(cat == Model.CategoryFilter)">@cat</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="rating" class="form-label">Rating</label>
                            <select class="form-select" id="rating" name="rating">
                                <option value="">All Ratings</option>
                                @for (int i = 5; i >= 1; i--)
                                {
                                    <option value="@i" selected="@(i == Model.RatingFilter)">@i ⭐</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach (var stat in Model.Statuses)
                                {
                                    <option value="@stat" selected="@(stat == Model.StatusFilter)">@stat</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search"></i> Filter
                            </button>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results Summary -->
            <div class="mb-3">
                <span class="text-muted">Showing @Model.TotalCount feedback(s)</span>
            </div>

            <!-- Feedback List -->
            @if (Model.Feedbacks.Any())
            {
                <div class="row">
                    @foreach (var feedback in Model.Feedbacks)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">@feedback.CustomerName</h6>
                                    <span class="badge bg-@GetStatusBadgeClass(feedback.Status)">@feedback.Status</span>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <small class="text-muted">@feedback.Email</small>
                                    </div>
                                    <div class="mb-2">
                                        <span class="badge bg-light text-dark">@feedback.Category</span>
                                        <span class="ms-2">@feedback.RatingStars</span>
                                    </div>
                                    <p class="card-text">
                                        @(feedback.FeedbackText.Length > 100 ? feedback.FeedbackText.Substring(0, 100) + "..." : feedback.FeedbackText)
                                    </p>
                                    <small class="text-muted">@feedback.FormattedTimestamp</small>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group w-100" role="group">
                                        <a asp-action="Details" asp-route-id="@feedback.Id" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@feedback.Id" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@feedback.Id" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No feedback found</h4>
                    <p class="text-muted">Try adjusting your search criteria or add some feedback.</p>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Feedback
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "New" => "primary",
            "In Progress" => "warning",
            "Resolved" => "success",
            "Closed" => "secondary",
            _ => "light"
        };
    }
}
