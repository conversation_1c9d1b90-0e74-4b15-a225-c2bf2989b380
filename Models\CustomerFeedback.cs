using Amazon.DynamoDBv2.DataModel;
using System.ComponentModel.DataAnnotations;

namespace demoDynamoDB.Models
{
    [DynamoDBTable("CustomerFeedback")]
    public class CustomerFeedback
    {
        [DynamoDBHashKey]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [DynamoDBProperty]
        [Required(ErrorMessage = "Customer name is required")]
        [StringLength(100, ErrorMessage = "Customer name cannot exceed 100 characters")]
        public string CustomerName { get; set; } = string.Empty;

        [DynamoDBProperty]
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [DynamoDBProperty]
        [Required(ErrorMessage = "Feedback text is required")]
        [StringLength(1000, ErrorMessage = "Feedback cannot exceed 1000 characters")]
        public string FeedbackText { get; set; } = string.Empty;

        [DynamoDBProperty]
        [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
        public int Rating { get; set; }

        [DynamoDBProperty]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [DynamoDBProperty]
        [StringLength(50, ErrorMessage = "Category cannot exceed 50 characters")]
        public string Category { get; set; } = "General";

        [DynamoDBProperty]
        public string Status { get; set; } = "New";

        // Helper properties for display
        [DynamoDBIgnore]
        public string FormattedTimestamp => Timestamp.ToString("yyyy-MM-dd HH:mm:ss");

        [DynamoDBIgnore]
        public string RatingStars => new string('★', Rating) + new string('☆', 5 - Rating);
    }
}
