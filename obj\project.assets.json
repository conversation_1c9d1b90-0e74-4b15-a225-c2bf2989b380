{"version": 3, "targets": {"net9.0": {"AWSSDK.Core/*******": {"type": "package", "compile": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.DynamoDBv2/4.0.1": {"type": "package", "dependencies": {"AWSSDK.Core": "[*******, 5.0.0)"}, "compile": {"lib/net8.0/AWSSDK.DynamoDBv2.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.DynamoDBv2.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.Extensions.NETCore.Setup/4.0.1": {"type": "package", "dependencies": {"AWSSDK.Core": "4.0.0", "Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0"}, "compile": {"lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net8.0/AWSSDK.Extensions.NETCore.Setup.targets": {}}}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/2.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.4.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}}}, "libraries": {"AWSSDK.Core/*******": {"sha512": "NG31J9azTmY1GKOZd+MFGLUsuD472AGz4BUceTiw0pdYkexD+xJ8kA87hDp/OTk2b6xgrhkQIpnttzIekkMSvw==", "type": "package", "path": "awssdk.core/*******", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.*******.nupkg.sha512", "awssdk.core.nuspec", "images/AWSLogo.png", "lib/net472/AWSSDK.Core.dll", "lib/net472/AWSSDK.Core.pdb", "lib/net472/AWSSDK.Core.xml", "lib/net8.0/AWSSDK.Core.dll", "lib/net8.0/AWSSDK.Core.pdb", "lib/net8.0/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.DynamoDBv2/4.0.1": {"sha512": "sOgLoQV2XylHzIgSLuzDALYl38bnFTrbpBFDyId8/QUkpaUiPUT1oGdfE0J78trODbTJYimpTRtQiO/GvfnQJQ==", "type": "package", "path": "awssdk.dynamodbv2/4.0.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.DynamoDBv2.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.dynamodbv2.4.0.1.nupkg.sha512", "awssdk.dynamodbv2.nuspec", "images/AWSLogo.png", "lib/net472/AWSSDK.DynamoDBv2.dll", "lib/net472/AWSSDK.DynamoDBv2.pdb", "lib/net472/AWSSDK.DynamoDBv2.xml", "lib/net8.0/AWSSDK.DynamoDBv2.dll", "lib/net8.0/AWSSDK.DynamoDBv2.pdb", "lib/net8.0/AWSSDK.DynamoDBv2.xml", "lib/netcoreapp3.1/AWSSDK.DynamoDBv2.dll", "lib/netcoreapp3.1/AWSSDK.DynamoDBv2.pdb", "lib/netcoreapp3.1/AWSSDK.DynamoDBv2.xml", "lib/netstandard2.0/AWSSDK.DynamoDBv2.dll", "lib/netstandard2.0/AWSSDK.DynamoDBv2.pdb", "lib/netstandard2.0/AWSSDK.DynamoDBv2.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.Extensions.NETCore.Setup/4.0.1": {"sha512": "avdsCcQSdw7iQpHAyKwcA15DmpwUXFZ2jV8YjNgIQq6T4hiJ8/ucFgAwxamibvUB3Wd06FXkWXh2AK8pWGfflA==", "type": "package", "path": "awssdk.extensions.netcore.setup/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ConfigurationSchema.json", "awssdk.extensions.netcore.setup.4.0.1.nupkg.sha512", "awssdk.extensions.netcore.setup.nuspec", "buildTransitive/net8.0/AWSSDK.Extensions.NETCore.Setup.targets", "buildTransitive/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.targets", "buildTransitive/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.targets", "images/AWSLogo.png", "lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll", "lib/net8.0/AWSSDK.Extensions.NETCore.Setup.pdb", "lib/net8.0/AWSSDK.Extensions.NETCore.Setup.xml", "lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.dll", "lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.pdb", "lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.xml", "lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll", "lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.pdb", "lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.xml"]}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"sha512": "rHFrXqMIvQNq51H8RYTO4IWmDOYh8NUzyqGlh0xHWTP6XYnKk7Ryinys2uDs+Vu88b3AMlM3gBBSs78m6OQpYQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.2.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"sha512": "6ZCllUYGFukkymSTx3Yr0G/ajRxoNJp7/FqSxSB4fGISST54ifBhgu4Nc0ItGi3i6DqwuNd8SUyObmiC++AO2Q==", "type": "package", "path": "microsoft.extensions.logging.abstractions/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.2.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec"]}, "Microsoft.Extensions.Primitives/2.0.0": {"sha512": "ukg53qNlqTrK38WA30b5qhw0GD7y3jdI9PHHASjdKyTcBHTevFM2o23tyk3pWCgAV27Bbkm+CPQ2zUe1ZOuYSA==", "type": "package", "path": "microsoft.extensions.primitives/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.2.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"sha512": "9dLLuBxr5GNmOfl2jSMcsHuteEg32BEfUotmmUkmZjpR3RpVHE8YQwt0ow3p6prwA1ME8WqDVZqrr8z6H8G+Kw==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["AWSSDK.DynamoDBv2 >= 4.0.1", "AWSSDK.Extensions.NETCore.Setup >= 4.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\demoDynamoDB\\demoDynamoDB\\demoDynamoDB.csproj", "projectName": "demoDynamoDB", "projectPath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\demoDynamoDB\\demoDynamoDB\\demoDynamoDB.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\demoDynamoDB\\demoDynamoDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.DynamoDBv2": {"target": "Package", "version": "[4.0.1, )"}, "AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[4.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}