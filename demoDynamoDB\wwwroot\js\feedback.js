// Customer Feedback System JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Character counter for textareas
    var textareas = document.querySelectorAll('textarea[maxlength]');
    textareas.forEach(function(textarea) {
        var maxLength = textarea.getAttribute('maxlength');
        var counter = document.createElement('div');
        counter.className = 'form-text text-end';
        textarea.parentNode.appendChild(counter);

        function updateCounter() {
            var remaining = maxLength - textarea.value.length;
            counter.textContent = textarea.value.length + '/' + maxLength + ' characters';
            
            if (remaining < 100) {
                counter.className = 'form-text text-end text-warning';
            } else if (remaining < 50) {
                counter.className = 'form-text text-end text-danger';
            } else {
                counter.className = 'form-text text-end text-muted';
            }
        }

        textarea.addEventListener('input', updateCounter);
        updateCounter();
    });

    // Rating stars interaction
    var ratingSelects = document.querySelectorAll('select[name="Rating"]');
    ratingSelects.forEach(function(select) {
        select.addEventListener('change', function() {
            var rating = parseInt(this.value);
            var starsDisplay = this.parentNode.querySelector('.rating-display');
            
            if (!starsDisplay) {
                starsDisplay = document.createElement('div');
                starsDisplay.className = 'rating-display mt-1';
                this.parentNode.appendChild(starsDisplay);
            }
            
            var stars = '';
            for (var i = 1; i <= 5; i++) {
                stars += i <= rating ? '⭐' : '☆';
            }
            starsDisplay.textContent = stars;
        });
        
        // Trigger initial display
        select.dispatchEvent(new Event('change'));
    });

    // Confirm delete actions
    var deleteButtons = document.querySelectorAll('a[href*="/Delete"], button[formaction*="/Delete"]');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this feedback? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });

    // Search functionality with debounce
    var searchInput = document.querySelector('input[name="searchTerm"]');
    if (searchInput) {
        var searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                // Auto-submit search form after 500ms of no typing
                var form = searchInput.closest('form');
                if (form) {
                    form.submit();
                }
            }, 500);
        });
    }

    // Filter change auto-submit
    var filterSelects = document.querySelectorAll('select[name="category"], select[name="rating"], select[name="status"]');
    filterSelects.forEach(function(select) {
        select.addEventListener('change', function() {
            var form = this.closest('form');
            if (form) {
                form.submit();
            }
        });
    });

    // Add loading state to forms
    var forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function() {
            var submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            }
        });
    });

    // Smooth scroll for anchor links
    var anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            var target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add fade-in animation to cards
    var cards = document.querySelectorAll('.card');
    cards.forEach(function(card, index) {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in');
    });

    // Status badge color updates
    function updateStatusBadges() {
        var statusBadges = document.querySelectorAll('.badge');
        statusBadges.forEach(function(badge) {
            var status = badge.textContent.trim().toLowerCase();
            badge.classList.remove('bg-primary', 'bg-warning', 'bg-success', 'bg-secondary');
            
            switch(status) {
                case 'new':
                    badge.classList.add('bg-primary');
                    break;
                case 'in progress':
                    badge.classList.add('bg-warning');
                    break;
                case 'resolved':
                    badge.classList.add('bg-success');
                    break;
                case 'closed':
                    badge.classList.add('bg-secondary');
                    break;
            }
        });
    }

    updateStatusBadges();

    // Copy to clipboard functionality
    function addCopyToClipboard() {
        var copyButtons = document.querySelectorAll('[data-copy]');
        copyButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var textToCopy = this.getAttribute('data-copy');
                navigator.clipboard.writeText(textToCopy).then(function() {
                    // Show success message
                    var originalText = button.textContent;
                    button.textContent = 'Copied!';
                    button.classList.add('btn-success');
                    
                    setTimeout(function() {
                        button.textContent = originalText;
                        button.classList.remove('btn-success');
                    }, 2000);
                });
            });
        });
    }

    addCopyToClipboard();

    // Real-time validation feedback
    var inputs = document.querySelectorAll('input[required], textarea[required], select[required]');
    inputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });

    // Email validation
    var emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (this.value && !emailRegex.test(this.value)) {
                this.classList.add('is-invalid');
                var feedback = this.parentNode.querySelector('.invalid-feedback');
                if (!feedback) {
                    feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    this.parentNode.appendChild(feedback);
                }
                feedback.textContent = 'Please enter a valid email address.';
            }
        });
    });
});

// Utility functions
function showNotification(message, type = 'info') {
    var notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(function() {
        notification.remove();
    }, 5000);
}

function formatDate(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}
