{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"demoDynamoDB/1.0.0": {"dependencies": {"AWSSDK.DynamoDBv2": "4.0.1", "AWSSDK.Extensions.NETCore.Setup": "4.0.1"}, "runtime": {"demoDynamoDB.dll": {}}}, "AWSSDK.Core/*******": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "*******"}}}, "AWSSDK.DynamoDBv2/4.0.1": {"dependencies": {"AWSSDK.Core": "*******"}, "runtime": {"lib/net8.0/AWSSDK.DynamoDBv2.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.1.0"}}}, "AWSSDK.Extensions.NETCore.Setup/4.0.1": {"dependencies": {"AWSSDK.Core": "*******", "Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0"}, "runtime": {"lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {}, "Microsoft.Extensions.Primitives/2.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.4.0"}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {}}}, "libraries": {"demoDynamoDB/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.Core/*******": {"type": "package", "serviceable": true, "sha512": "sha512-NG31J9azTmY1GKOZd+MFGLUsuD472AGz4BUceTiw0pdYkexD+xJ8kA87hDp/OTk2b6xgrhkQIpnttzIekkMSvw==", "path": "awssdk.core/*******", "hashPath": "awssdk.core.*******.nupkg.sha512"}, "AWSSDK.DynamoDBv2/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sOgLoQV2XylHzIgSLuzDALYl38bnFTrbpBFDyId8/QUkpaUiPUT1oGdfE0J78trODbTJYimpTRtQiO/GvfnQJQ==", "path": "awssdk.dynamodbv2/4.0.1", "hashPath": "awssdk.dynamodbv2.4.0.1.nupkg.sha512"}, "AWSSDK.Extensions.NETCore.Setup/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-avdsCcQSdw7iQpHAyKwcA15DmpwUXFZ2jV8YjNgIQq6T4hiJ8/ucFgAwxamibvUB3Wd06FXkWXh2AK8pWGfflA==", "path": "awssdk.extensions.netcore.setup/4.0.1", "hashPath": "awssdk.extensions.netcore.setup.4.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rHFrXqMIvQNq51H8RYTO4IWmDOYh8NUzyqGlh0xHWTP6XYnKk7Ryinys2uDs+Vu88b3AMlM3gBBSs78m6OQpYQ==", "path": "microsoft.extensions.configuration.abstractions/2.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ZCllUYGFukkymSTx3Yr0G/ajRxoNJp7/FqSxSB4fGISST54ifBhgu4Nc0ItGi3i6DqwuNd8SUyObmiC++AO2Q==", "path": "microsoft.extensions.logging.abstractions/2.0.0", "hashPath": "microsoft.extensions.logging.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ukg53qNlqTrK38WA30b5qhw0GD7y3jdI9PHHASjdKyTcBHTevFM2o23tyk3pWCgAV27Bbkm+CPQ2zUe1ZOuYSA==", "path": "microsoft.extensions.primitives/2.0.0", "hashPath": "microsoft.extensions.primitives.2.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-9dLLuBxr5GNmOfl2jSMcsHuteEg32BEfUotmmUkmZjpR3RpVHE8YQwt0ow3p6prwA1ME8WqDVZqrr8z6H8G+Kw==", "path": "system.runtime.compilerservices.unsafe/4.4.0", "hashPath": "system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512"}}}