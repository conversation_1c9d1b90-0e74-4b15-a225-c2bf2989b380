using System.ComponentModel.DataAnnotations;

namespace demoDynamoDB.Models
{
    public class FeedbackListViewModel
    {
        public List<CustomerFeedback> Feedbacks { get; set; } = new();
        public string SearchTerm { get; set; } = string.Empty;
        public string CategoryFilter { get; set; } = string.Empty;
        public int? RatingFilter { get; set; }
        public string StatusFilter { get; set; } = string.Empty;
        public int TotalCount { get; set; }
        public List<string> Categories { get; set; } = new();
        public List<string> Statuses { get; set; } = new();
    }

    public class CreateFeedbackViewModel
    {
        [Required(ErrorMessage = "Customer name is required")]
        [StringLength(100, ErrorMessage = "Customer name cannot exceed 100 characters")]
        public string CustomerName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Feedback text is required")]
        [StringLength(1000, ErrorMessage = "Feedback cannot exceed 1000 characters")]
        public string FeedbackText { get; set; } = string.Empty;

        [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
        public int Rating { get; set; } = 5;

        [StringLength(50, ErrorMessage = "Category cannot exceed 50 characters")]
        public string Category { get; set; } = "General";

        public List<string> AvailableCategories { get; set; } = new()
        {
            "General", "Product Quality", "Customer Service", "Delivery", "Website", "Pricing", "Other"
        };
    }

    public class EditFeedbackViewModel : CreateFeedbackViewModel
    {
        public string Id { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string Status { get; set; } = "New";

        public List<string> AvailableStatuses { get; set; } = new()
        {
            "New", "In Progress", "Resolved", "Closed"
        };
    }
}
