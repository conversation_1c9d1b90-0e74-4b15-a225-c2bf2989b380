using System.ComponentModel.DataAnnotations;

namespace demoDynamoDB.Models
{
    public class FeedbackListViewModel
    {
        public List<CustomerFeedback> Feedbacks { get; set; } = new();
        public string SearchTerm { get; set; } = string.Empty;
        public string CategoryFilter { get; set; } = string.Empty;
        public int? RatingFilter { get; set; }
        public string StatusFilter { get; set; } = string.Empty;
        public string PriorityFilter { get; set; } = string.Empty;
        public string ResponseStatusFilter { get; set; } = string.Empty;
        public string AssignedToFilter { get; set; } = string.Empty;
        public DateTime? DateFromFilter { get; set; }
        public DateTime? DateToFilter { get; set; }
        public string SortBy { get; set; } = "Timestamp";
        public string SortDirection { get; set; } = "desc";
        public int TotalCount { get; set; }
        public List<string> Categories { get; set; } = new();
        public List<string> Statuses { get; set; } = new();
        public List<string> Priorities { get; set; } = new() { "High", "Medium", "Low" };
        public List<string> ResponseStatuses { get; set; } = new() { "Pending Response", "Responded", "Follow-up Required", "Escalated" };
        public List<string> Administrators { get; set; } = new();
        public bool ShowEscalatedOnly { get; set; } = false;
    }

    public class CreateFeedbackViewModel
    {
        [Required(ErrorMessage = "Customer name is required")]
        [StringLength(100, ErrorMessage = "Customer name cannot exceed 100 characters")]
        public string CustomerName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Feedback text is required")]
        [StringLength(1000, ErrorMessage = "Feedback cannot exceed 1000 characters")]
        public string FeedbackText { get; set; } = string.Empty;

        [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
        public int Rating { get; set; } = 5;

        [StringLength(50, ErrorMessage = "Category cannot exceed 50 characters")]
        public string Category { get; set; } = "General";

        public List<string> AvailableCategories { get; set; } = new()
        {
            "General", "Product Quality", "Customer Service", "Delivery", "Website", "Pricing", "Other"
        };
    }

    public class EditFeedbackViewModel : CreateFeedbackViewModel
    {
        public string Id { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string Status { get; set; } = "New";

        public List<string> AvailableStatuses { get; set; } = new()
        {
            "New", "In Progress", "Resolved", "Closed"
        };

        public List<string> AvailablePriorities { get; set; } = new()
        {
            "High", "Medium", "Low"
        };

        public List<string> AvailableResponseStatuses { get; set; } = new()
        {
            "Pending Response", "Responded", "Follow-up Required", "Escalated"
        };

        public List<string> AvailableAdministrators { get; set; } = new()
        {
            "Admin1", "Admin2", "Admin3", "Unassigned"
        };
    }

    public class ResponseViewModel
    {
        [Required(ErrorMessage = "Feedback ID is required")]
        public string FeedbackId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Response text is required")]
        [StringLength(2000, ErrorMessage = "Response cannot exceed 2000 characters")]
        public string ResponseText { get; set; } = string.Empty;

        [Required(ErrorMessage = "Administrator name is required")]
        [StringLength(100, ErrorMessage = "Administrator name cannot exceed 100 characters")]
        public string AdminName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "Response type cannot exceed 50 characters")]
        public string ResponseType { get; set; } = "Standard";

        public bool SendEmail { get; set; } = true;
        public string EmailTemplateType { get; set; } = "Standard";
        public string CustomEmailSubject { get; set; } = string.Empty;
        public string CustomEmailMessage { get; set; } = string.Empty;
        public bool IsInternal { get; set; } = false;

        // For display
        public CustomerFeedback? Feedback { get; set; }
        public List<CustomerResponse> ExistingResponses { get; set; } = new();

        public List<string> AvailableResponseTypes { get; set; } = new()
        {
            "Standard", "Acknowledgment", "Resolution", "Follow-up", "Escalation"
        };

        public List<string> AvailableEmailTemplates { get; set; } = new()
        {
            "Standard", "Acknowledgment", "Resolution", "Follow-up", "Escalation"
        };
    }

    public class ManagementDashboardViewModel
    {
        public int TotalFeedbacks { get; set; }
        public int PendingResponses { get; set; }
        public int HighPriorityFeedbacks { get; set; }
        public int EscalatedFeedbacks { get; set; }
        public int TodaysFeedbacks { get; set; }
        public double AverageResponseTime { get; set; }
        public double AverageRating { get; set; }

        public List<CustomerFeedback> UrgentFeedbacks { get; set; } = new();
        public List<CustomerFeedback> RecentFeedbacks { get; set; } = new();
        public List<CustomerFeedback> EscalatedFeedbacks { get; set; } = new();

        public Dictionary<string, int> FeedbacksByPriority { get; set; } = new();
        public Dictionary<string, int> FeedbacksByResponseStatus { get; set; } = new();
        public Dictionary<string, int> FeedbacksByCategory { get; set; } = new();
        public Dictionary<string, int> FeedbacksByAssignee { get; set; } = new();

        public string FormattedAverageResponseTime => AverageResponseTime.ToString("F1") + " hours";
        public string FormattedAverageRating => AverageRating.ToString("F1");
    }

    public class BulkActionViewModel
    {
        [Required]
        public List<string> SelectedFeedbackIds { get; set; } = new();

        [Required(ErrorMessage = "Action is required")]
        public string Action { get; set; } = string.Empty;

        // For status updates
        public string NewStatus { get; set; } = string.Empty;
        public string NewPriority { get; set; } = string.Empty;
        public string AssignTo { get; set; } = string.Empty;

        // For bulk responses
        public string ResponseText { get; set; } = string.Empty;
        public string ResponseType { get; set; } = "Standard";
        public bool SendEmails { get; set; } = true;
        public string EmailTemplateType { get; set; } = "Standard";

        public List<string> AvailableActions { get; set; } = new()
        {
            "Update Status", "Update Priority", "Assign", "Add Response", "Send Emails", "Escalate"
        };
    }
}
