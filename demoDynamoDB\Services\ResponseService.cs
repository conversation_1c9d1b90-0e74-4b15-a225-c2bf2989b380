using demoDynamoDB.Models;

namespace demoDynamoDB.Services
{
    public class ResponseService : IResponseService
    {
        private readonly IDynamoDBService _dynamoDBService;
        private readonly IEmailService _emailService;
        private readonly ILogger<ResponseService> _logger;
        private readonly IConfiguration _configuration;

        public ResponseService(IDynamoDBService dynamoDBService, IEmailService emailService, ILogger<ResponseService> logger, IConfiguration configuration)
        {
            _dynamoDBService = dynamoDBService;
            _emailService = emailService;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<CustomerResponse> CreateResponseAsync(ResponseViewModel model)
        {
            try
            {
                // Create the response
                var response = new CustomerResponse
                {
                    FeedbackId = model.FeedbackId,
                    ResponseText = model.ResponseText,
                    AdminName = model.AdminName,
                    ResponseType = model.ResponseType,
                    IsInternal = model.IsInternal,
                    ResponseDate = DateTime.UtcNow
                };

                // Save response to database (you'll need to implement this in DynamoDBService)
                var savedResponse = await SaveResponseAsync(response);

                // Update feedback status
                var feedback = await _dynamoDBService.GetFeedbackByIdAsync(model.FeedbackId);
                if (feedback != null)
                {
                    feedback.ResponseStatus = model.IsInternal ? feedback.ResponseStatus : "Responded";
                    feedback.LastResponseDate = DateTime.UtcNow;
                    
                    // Update status based on response type
                    if (model.ResponseType == "Resolution")
                    {
                        feedback.Status = "Resolved";
                        feedback.ResponseStatus = "Responded";
                    }
                    else if (model.ResponseType == "Escalation")
                    {
                        feedback.IsEscalated = true;
                        feedback.EscalatedDate = DateTime.UtcNow;
                        feedback.ResponseStatus = "Escalated";
                    }

                    await _dynamoDBService.UpdateFeedbackAsync(feedback);

                    // Send email if requested and not internal
                    if (model.SendEmail && !model.IsInternal)
                    {
                        try
                        {
                            var emailResult = await _emailService.SendResponseEmailAsync(feedback, savedResponse, model.EmailTemplateType);
                            savedResponse.IsEmailSent = emailResult.Success;
                            savedResponse.EmailSentDate = emailResult.Success ? emailResult.SentDate : null;
                            savedResponse.EmailStatus = emailResult.Success ? "Sent" : "Failed";
                            
                            feedback.EmailStatus = emailResult.Success ? "Sent" : "Failed";
                            await _dynamoDBService.UpdateFeedbackAsync(feedback);
                            await UpdateResponseAsync(savedResponse);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to send email for response {ResponseId}", savedResponse.Id);
                            savedResponse.EmailStatus = "Failed";
                            await UpdateResponseAsync(savedResponse);
                        }
                    }
                }

                return savedResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create response for feedback {FeedbackId}", model.FeedbackId);
                throw;
            }
        }

        public async Task<List<CustomerResponse>> GetResponsesByFeedbackIdAsync(string feedbackId)
        {
            // This would need to be implemented in DynamoDBService
            // For now, return empty list
            return new List<CustomerResponse>();
        }

        public async Task<CustomerResponse?> GetResponseByIdAsync(string responseId)
        {
            // This would need to be implemented in DynamoDBService
            return null;
        }

        public async Task<CustomerResponse> UpdateResponseAsync(CustomerResponse response)
        {
            // This would need to be implemented in DynamoDBService
            return response;
        }

        public async Task<bool> DeleteResponseAsync(string responseId)
        {
            // This would need to be implemented in DynamoDBService
            return true;
        }

        public async Task<List<CustomerResponse>> GetAllResponsesAsync()
        {
            // This would need to be implemented in DynamoDBService
            return new List<CustomerResponse>();
        }

        public async Task<ManagementDashboardViewModel> GetManagementDashboardAsync()
        {
            var allFeedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var today = DateTime.UtcNow.Date;

            var dashboard = new ManagementDashboardViewModel
            {
                TotalFeedbacks = allFeedbacks.Count,
                PendingResponses = allFeedbacks.Count(f => f.ResponseStatus == "Pending Response"),
                HighPriorityFeedbacks = allFeedbacks.Count(f => f.Priority == "High"),
                EscalatedFeedbacks = allFeedbacks.Count(f => f.IsEscalated),
                TodaysFeedbacks = allFeedbacks.Count(f => f.Timestamp.Date == today),
                AverageRating = allFeedbacks.Any() ? allFeedbacks.Average(f => f.Rating) : 0
            };

            // Calculate average response time
            var respondedFeedbacks = allFeedbacks.Where(f => f.LastResponseDate.HasValue).ToList();
            if (respondedFeedbacks.Any())
            {
                var responseTimes = respondedFeedbacks.Select(f => (f.LastResponseDate!.Value - f.Timestamp).TotalHours);
                dashboard.AverageResponseTime = responseTimes.Average();
            }

            // Get urgent feedbacks (high priority or old pending responses)
            var urgentCutoff = DateTime.UtcNow.AddHours(-24);
            dashboard.UrgentFeedbacks = allFeedbacks
                .Where(f => f.Priority == "High" || 
                           (f.ResponseStatus == "Pending Response" && f.Timestamp < urgentCutoff))
                .OrderByDescending(f => f.Timestamp)
                .Take(10)
                .ToList();

            dashboard.RecentFeedbacks = allFeedbacks
                .OrderByDescending(f => f.Timestamp)
                .Take(10)
                .ToList();

            dashboard.EscalatedFeedbacks = allFeedbacks
                .Where(f => f.IsEscalated)
                .OrderByDescending(f => f.EscalatedDate)
                .Take(10)
                .ToList();

            // Statistics
            dashboard.FeedbacksByPriority = allFeedbacks
                .GroupBy(f => f.Priority)
                .ToDictionary(g => g.Key, g => g.Count());

            dashboard.FeedbacksByResponseStatus = allFeedbacks
                .GroupBy(f => f.ResponseStatus)
                .ToDictionary(g => g.Key, g => g.Count());

            dashboard.FeedbacksByCategory = allFeedbacks
                .GroupBy(f => f.Category)
                .ToDictionary(g => g.Key, g => g.Count());

            dashboard.FeedbacksByAssignee = allFeedbacks
                .GroupBy(f => string.IsNullOrEmpty(f.AssignedTo) ? "Unassigned" : f.AssignedTo)
                .ToDictionary(g => g.Key, g => g.Count());

            return dashboard;
        }

        public async Task<BulkEmailResult> ProcessBulkActionAsync(BulkActionViewModel model)
        {
            var result = new BulkEmailResult();

            try
            {
                var feedbacks = new List<CustomerFeedback>();
                foreach (var feedbackId in model.SelectedFeedbackIds)
                {
                    var feedback = await _dynamoDBService.GetFeedbackByIdAsync(feedbackId);
                    if (feedback != null)
                    {
                        feedbacks.Add(feedback);
                    }
                }

                switch (model.Action)
                {
                    case "Update Status":
                        await UpdateFeedbacksStatusAsync(feedbacks, model.NewStatus);
                        break;
                    case "Update Priority":
                        await UpdateFeedbacksPriorityAsync(feedbacks, model.NewPriority);
                        break;
                    case "Assign":
                        await AssignFeedbacksAsync(feedbacks, model.AssignTo);
                        break;
                    case "Add Response":
                        await AddBulkResponsesAsync(feedbacks, model.ResponseText, model.ResponseType);
                        break;
                    case "Send Emails":
                        result = await _emailService.SendBulkEmailsAsync(feedbacks, model.EmailTemplateType);
                        break;
                    case "Escalate":
                        await EscalateFeedbacksAsync(feedbacks);
                        break;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process bulk action {Action}", model.Action);
                result.Errors.Add($"Bulk action failed: {ex.Message}");
                return result;
            }
        }

        public async Task<bool> EscalateFeedbackAsync(string feedbackId, string reason)
        {
            try
            {
                var feedback = await _dynamoDBService.GetFeedbackByIdAsync(feedbackId);
                if (feedback != null)
                {
                    feedback.IsEscalated = true;
                    feedback.EscalatedDate = DateTime.UtcNow;
                    feedback.ResponseStatus = "Escalated";
                    feedback.Priority = "High";
                    feedback.InternalNotes += $"\nEscalated on {DateTime.UtcNow:yyyy-MM-dd HH:mm}: {reason}";

                    await _dynamoDBService.UpdateFeedbackAsync(feedback);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to escalate feedback {FeedbackId}", feedbackId);
                return false;
            }
        }

        public async Task<bool> AssignFeedbackAsync(string feedbackId, string adminName)
        {
            try
            {
                var feedback = await _dynamoDBService.GetFeedbackByIdAsync(feedbackId);
                if (feedback != null)
                {
                    feedback.AssignedTo = adminName;
                    await _dynamoDBService.UpdateFeedbackAsync(feedback);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to assign feedback {FeedbackId} to {AdminName}", feedbackId, adminName);
                return false;
            }
        }

        public async Task<List<CustomerFeedback>> GetFeedbacksRequiringAttentionAsync()
        {
            var allFeedbacks = await _dynamoDBService.GetAllFeedbacksAsync();
            var attentionCutoff = DateTime.UtcNow.AddHours(-24);

            return allFeedbacks
                .Where(f => f.Priority == "High" || 
                           f.IsEscalated ||
                           (f.ResponseStatus == "Pending Response" && f.Timestamp < attentionCutoff))
                .OrderByDescending(f => f.Priority == "High" ? 3 : f.IsEscalated ? 2 : 1)
                .ThenByDescending(f => f.Timestamp)
                .ToList();
        }

        // Helper methods
        private async Task<CustomerResponse> SaveResponseAsync(CustomerResponse response)
        {
            // This would need to be implemented in DynamoDBService
            // For now, just return the response with an ID
            response.Id = Guid.NewGuid().ToString();
            return response;
        }

        private async Task UpdateFeedbacksStatusAsync(List<CustomerFeedback> feedbacks, string newStatus)
        {
            foreach (var feedback in feedbacks)
            {
                feedback.Status = newStatus;
                await _dynamoDBService.UpdateFeedbackAsync(feedback);
            }
        }

        private async Task UpdateFeedbacksPriorityAsync(List<CustomerFeedback> feedbacks, string newPriority)
        {
            foreach (var feedback in feedbacks)
            {
                feedback.Priority = newPriority;
                await _dynamoDBService.UpdateFeedbackAsync(feedback);
            }
        }

        private async Task AssignFeedbacksAsync(List<CustomerFeedback> feedbacks, string assignTo)
        {
            foreach (var feedback in feedbacks)
            {
                feedback.AssignedTo = assignTo;
                await _dynamoDBService.UpdateFeedbackAsync(feedback);
            }
        }

        private async Task AddBulkResponsesAsync(List<CustomerFeedback> feedbacks, string responseText, string responseType)
        {
            foreach (var feedback in feedbacks)
            {
                var response = new CustomerResponse
                {
                    FeedbackId = feedback.Id,
                    ResponseText = responseText,
                    AdminName = "System",
                    ResponseType = responseType,
                    ResponseDate = DateTime.UtcNow
                };

                await SaveResponseAsync(response);
                
                feedback.ResponseStatus = "Responded";
                feedback.LastResponseDate = DateTime.UtcNow;
                await _dynamoDBService.UpdateFeedbackAsync(feedback);
            }
        }

        private async Task EscalateFeedbacksAsync(List<CustomerFeedback> feedbacks)
        {
            foreach (var feedback in feedbacks)
            {
                await EscalateFeedbackAsync(feedback.Id, "Bulk escalation");
            }
        }
    }
}
