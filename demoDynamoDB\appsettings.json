{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AWS": {"Profile": "default", "Region": "us-east-1"}, "DynamoDB": {"LocalServiceUrl": "http://localhost:8000", "TableName": "CustomerFeedback"}, "Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "your-app-password", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "Customer Feedback Team"}, "Application": {"Administrators": ["Admin1", "Admin2", "Admin3"], "DefaultResponseTimeHours": 24, "EscalationTimeHours": 72, "EnableEmailNotifications": true}}