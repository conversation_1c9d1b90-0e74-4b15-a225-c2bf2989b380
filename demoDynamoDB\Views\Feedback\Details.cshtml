@model demoDynamoDB.Models.CustomerFeedback
@{
    ViewData["Title"] = "Feedback Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4><i class="fas fa-comment-dots"></i> Feedback Details</h4>
                    <span class="badge bg-@GetStatusBadgeClass(Model.Status) fs-6">@Model.Status</span>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Customer Information</h6>
                            <div class="mb-2">
                                <strong>Name:</strong> @Model.CustomerName
                            </div>
                            <div class="mb-2">
                                <strong>Email:</strong> 
                                <a href="mailto:@Model.Email">@Model.Email</a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Feedback Information</h6>
                            <div class="mb-2">
                                <strong>Category:</strong> 
                                <span class="badge bg-light text-dark">@Model.Category</span>
                            </div>
                            <div class="mb-2">
                                <strong>Rating:</strong> 
                                <span class="fs-5">@Model.RatingStars</span>
                                <span class="text-muted">(@Model.Rating/5)</span>
                            </div>
                            <div class="mb-2">
                                <strong>Submitted:</strong> @Model.FormattedTimestamp
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-muted">Feedback Content</h6>
                        <div class="border rounded p-3 bg-light">
                            <p class="mb-0">@Model.FeedbackText</p>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Actions Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-tools"></i> Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Status Management</h6>
                            @if (Model.Status == "New")
                            {
                                <p class="text-muted">This feedback is new and needs attention.</p>
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-outline-warning">
                                    Mark as In Progress
                                </a>
                            }
                            else if (Model.Status == "In Progress")
                            {
                                <p class="text-muted">This feedback is being processed.</p>
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-outline-success">
                                    Mark as Resolved
                                </a>
                            }
                            else if (Model.Status == "Resolved")
                            {
                                <p class="text-muted">This feedback has been resolved.</p>
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-outline-secondary">
                                    Mark as Closed
                                </a>
                            }
                            else
                            {
                                <p class="text-muted">This feedback is closed.</p>
                            }
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Customer Contact</h6>
                            <p class="text-muted">Reach out to the customer directly.</p>
                            <a href="mailto:@Model.Email?subject=Re: Your Feedback&body=Dear @Model.CustomerName,%0A%0AThank you for your feedback..." 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-envelope"></i> Send Email
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "New" => "primary",
            "In Progress" => "warning",
            "Resolved" => "success",
            "Closed" => "secondary",
            _ => "light"
        };
    }
}
